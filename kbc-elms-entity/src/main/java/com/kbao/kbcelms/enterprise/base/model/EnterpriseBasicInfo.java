package com.kbao.kbcelms.enterprise.base.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 企业基本信息（含企业联系方式）
 * 对应天眼查API：818-企业基本信息（含企业联系方式）
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@Document(collection = "enterprise_basic_info")
@ApiModel(value = "EnterpriseBasicInfo", description = "企业基本信息")
public class EnterpriseBasicInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 统一社会信用代码
     */
    @Indexed
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 曾用名
     */
    @ApiModelProperty(value = "曾用名")
    private String historyNames;

    /**
     * 企业状态
     */
    @ApiModelProperty(value = "企业状态")
    private String regStatus;

    /**
     * 全部邮箱列表
     */
    @ApiModelProperty(value = "全部邮箱列表")
    private List<String> emailList;

    /**
     * 股票号
     */
    @ApiModelProperty(value = "股票号")
    private String bondNum;

    /**
     * 股票名
     */
    @ApiModelProperty(value = "股票名")
    private String bondName;

    /**
     * 法人类型（1-人，2-公司）
     */
    @ApiModelProperty(value = "法人类型（1-人，2-公司）")
    private Integer type;

    /**
     * 吊销原因
     */
    @ApiModelProperty(value = "吊销原因")
    private String revokeReason;

    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String property3;

    /**
     * 股票曾用名
     */
    @ApiModelProperty(value = "股票曾用名")
    private String usedBondName;

    /**
     * 核准时间（时间戳）
     */
    @ApiModelProperty(value = "核准时间（时间戳）")
    private Long approvedTime;

    /**
     * 企业id
     */
    @ApiModelProperty(value = "企业id")
    private Long companyId;

    /**
     * 组织机构代码
     */
    @ApiModelProperty(value = "组织机构代码")
    private String orgNumber;

    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    private String businessScope;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(value = "纳税人识别号")
    private String taxNumber;

    /**
     * 注册资本币种
     */
    @ApiModelProperty(value = "注册资本币种")
    private String regCapitalCurrency;

    /**
     * 企业标签
     */
    @ApiModelProperty(value = "企业标签")
    private String tags;

    /**
     * 企业联系方式
     */
    @ApiModelProperty(value = "企业联系方式")
    private String phoneNumber;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String district;

    /**
     * 经济功能区1
     */
    @ApiModelProperty(value = "经济功能区1")
    private String economicFunctionZone1;

    /**
     * 经济功能区2
     */
    @ApiModelProperty(value = "经济功能区2")
    private String economicFunctionZone2;

    /**
     * 企业名
     */
    @ApiModelProperty(value = "企业名")
    private String name;

    /**
     * 企业评分（万分制）
     */
    @ApiModelProperty(value = "企业评分（万分制）")
    private Integer percentileScore;

    /**
     * 国民经济行业分类
     */
    @ApiModelProperty(value = "国民经济行业分类")
    private IndustryAll industryAll;

    /**
     * 是否是小微企业（0-否，1-是）
     */
    @ApiModelProperty(value = "是否是小微企业（0-否，1-是）")
    private Integer isMicroEnt;

    /**
     * 注销日期（时间戳）
     */
    @ApiModelProperty(value = "注销日期（时间戳）")
    private Long cancelDate;

    /**
     * 行政区划代码
     */
    @ApiModelProperty(value = "行政区划代码")
    private String districtCode;

    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本")
    private String regCapital;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 人员规模
     */
    @ApiModelProperty(value = "人员规模")
    private String staffNumRange;

    /**
     * 曾用名列表
     */
    @ApiModelProperty(value = "曾用名列表")
    private List<String> historyNameList;

    /**
     * 行业
     */
    @ApiModelProperty(value = "行业")
    private String industry;

    /**
     * 吊销日期（时间戳）
     */
    @ApiModelProperty(value = "吊销日期（时间戳）")
    private Long revokeDate;

    /**
     * 更新时间（时间戳）
     */
    @ApiModelProperty(value = "更新时间（时间戳）")
    private Long updateTimes;

    /**
     * 商业登记号
     */
    @ApiModelProperty(value = "商业登记号")
    private String BRNNumber;

    /**
     * 法人
     */
    @ApiModelProperty(value = "法人")
    private String legalPersonName;

    /**
     * 注册号
     */
    @ApiModelProperty(value = "注册号")
    private String regNumber;

    /**
     * 经营开始时间（时间戳）
     */
    @ApiModelProperty(value = "经营开始时间（时间戳）")
    private Long fromTime;

    /**
     * 参保人数
     */
    @ApiModelProperty(value = "参保人数")
    private Integer socialStaffNum;

    /**
     * 实收注册资本币种
     */
    @ApiModelProperty(value = "实收注册资本币种")
    private String actualCapitalCurrency;

    /**
     * 简称
     */
    @ApiModelProperty(value = "简称")
    private String alias;

    /**
     * 企业类型
     */
    @ApiModelProperty(value = "企业类型")
    private String companyOrgType;

    /**
     * 注销原因
     */
    @ApiModelProperty(value = "注销原因")
    private String cancelReason;

    /**
     * 经营结束时间（时间戳）
     */
    @ApiModelProperty(value = "经营结束时间（时间戳）")
    private Long toTime;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 实收注册资金
     */
    @ApiModelProperty(value = "实收注册资金")
    private String actualCapital;

    /**
     * 成立日期（时间戳）
     */
    @ApiModelProperty(value = "成立日期（时间戳）")
    private Long establishTime;

    /**
     * 登记机关
     */
    @ApiModelProperty(value = "登记机关")
    private String regInstitute;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String regLocation;

    /**
     * 网址
     */
    @ApiModelProperty(value = "网址")
    private String websiteList;

    /**
     * 股票类型
     */
    @ApiModelProperty(value = "股票类型")
    private String bondType;

    /**
     * 省份简称
     */
    @ApiModelProperty(value = "省份简称")
    private String base;

    /**
     * 企业规模（从1149接口获取）
     */
    @ApiModelProperty(value = "企业规模")
    private String scale;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 租户ID
     */
    @Indexed
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 国民经济行业分类内部类
     */
    @Data
    public static class IndustryAll implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 行业门类
         */
        @ApiModelProperty(value = "行业门类")
        private String category;

        /**
         * 行业中类
         */
        @ApiModelProperty(value = "行业中类")
        private String categoryMiddle;

        /**
         * 行业大类
         */
        @ApiModelProperty(value = "行业大类")
        private String categoryBig;

        /**
         * 行业小类
         */
        @ApiModelProperty(value = "行业小类")
        private String categorySmall;

        /**
         * 门类行业代码
         */
        @ApiModelProperty(value = "门类行业代码")
        private String categoryCodeFirst;

        /**
         * 大类行业代码
         */
        @ApiModelProperty(value = "大类行业代码")
        private String categoryCodeSecond;

        /**
         * 中类行业代码
         */
        @ApiModelProperty(value = "中类行业代码")
        private String categoryCodeThird;

        /**
         * 小类行业代码
         */
        @ApiModelProperty(value = "小类行业代码")
        private String categoryCodeFourth;
    }
}
