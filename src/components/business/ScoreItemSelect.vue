<template>
  <el-select
    v-model="currentValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :multiple="multiple"
    :filterable="true"
    :remote="true"
    :remote-method="handleSearch"
    :loading="loading"
    :loading-text="loadingText"
    :no-data-text="noDataText"
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @clear="handleClear"
  >
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.label"
      :value="item.value"
      :disabled="item.status === 0"
    >
      <span>{{ item.label }}</span>
    </el-option>

    <!-- 加载更多 -->
    <el-option
      v-if="hasMore && !loading"
      :value="'__load_more__'"
      :disabled="true"
      style="text-align: center; color: #409EFF; cursor: pointer;"
      @click.native="loadMore"
    >
      <i class="el-icon-more"></i> 加载更多
    </el-option>

    <!-- 加载中 -->
    <el-option
      v-if="loading && options.length > 0"
      :value="'__loading__'"
      :disabled="true"
      style="text-align: center; color: #909399;"
    >
      <i class="el-icon-loading"></i> 加载中...
    </el-option>
  </el-select>
</template>

<script>

import {getScoreItemOptions} from "@/api/riskMatrix";

export default {
  name: 'ScoreItemSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择评分项'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    category: {
      type: String,
      default: ''
    },
    enterpriseType: {
      type: String,
      default: ''
    },
    status: {
      type: Number,
      default: 1 // 默认只显示启用的
    },
    pageSize: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      currentValue: this.value,
      options: [],
      loading: false,
      currentPage: 1,
      total: 0,
      hasMore: false,
      searchKeyword: '',
      loadingText: '加载中...',
      noDataText: '暂无数据'
    }
  },
  watch: {
    value(newVal) {
      this.currentValue = newVal
    },
    currentValue(newVal) {
      this.$emit('input', newVal)
    },
    category() {
      this.resetAndLoad()
    },
    enterpriseType() {
      this.resetAndLoad()
    },
    status() {
      this.resetAndLoad()
    }
  },
  mounted() {
    this.loadOptions()
  },
  methods: {
    /**
     * 加载选项数据
     */
    async loadOptions(isLoadMore = false) {
      if (this.loading) return

      this.loading = true

      try {
        const params = {
          pageNum: isLoadMore ? this.currentPage + 1 : 1,
          pageSize: this.pageSize,
          param: {
            keyword: this.searchKeyword,
            category: this.category,
            enterpriseType: this.enterpriseType,
            status: this.status
          }
        }

        const response = await getScoreItemOptions(params)

        if (response) {
          const { list, total, pageNum, pages } = response

          if (isLoadMore) {
            // 加载更多：追加数据
            this.options = [...this.options, ...list]
            this.currentPage = pageNum
          } else {
            // 首次加载或搜索：替换数据
            this.options = list || []
            this.currentPage = 1
          }

          this.total = total || 0
          this.hasMore = pageNum < pages

          // 如果没有数据，更新提示文本
          if (this.options.length === 0) {
            this.noDataText = this.searchKeyword ? '未找到相关评分项' : '暂无评分项数据'
          }
        } else {
          this.$message.error(response.message || '获取评分项列表失败')
          this.options = []
          this.hasMore = false
        }
      } catch (error) {
        console.error('获取评分项选项失败:', error)
        this.$message.error('获取评分项列表失败')
        this.options = []
        this.hasMore = false
      } finally {
        this.loading = false
      }
    },

    /**
     * 搜索处理
     */
    handleSearch(keyword) {
      this.searchKeyword = keyword || ''
      this.currentPage = 1
      this.hasMore = false

      // 防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.loadOptions()
      }, 300)
    },

    /**
     * 加载更多
     */
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadOptions(true)
      }
    },

    /**
     * 下拉框显示/隐藏处理
     */
    handleVisibleChange(visible) {
      if (visible && this.options.length === 0) {
        this.loadOptions()
      }
      this.$emit('visible-change', visible)
    },

    /**
     * 值变化处理
     */
    handleChange(value) {
      this.$emit('change', value)

      // 如果是多选且选中了"加载更多"，需要移除这个值
      if (this.multiple && Array.isArray(value)) {
        const filteredValue = value.filter(v => v !== '__load_more__' && v !== '__loading__')
        if (filteredValue.length !== value.length) {
          this.currentValue = filteredValue
        }
      }
    },

    /**
     * 清空处理
     */
    handleClear() {
      this.$emit('clear')
    },

    /**
     * 重置并重新加载
     */
    resetAndLoad() {
      this.currentPage = 1
      this.hasMore = false
      this.searchKeyword = ''
      this.options = []
      this.loadOptions()
    },

    /**
     * 刷新数据
     */
    refresh() {
      this.resetAndLoad()
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 300px !important;
  min-width: 300px;
}

.el-select-dropdown__item.is-disabled {
  cursor: default;
}
</style>
