<template>
	<div class="">
		<el-dialog
			:title="title"
			:visible.sync="show"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="isShowClose"
			:width="width"
			:center="isCenter"
			class="dt-popup"
			:class="{isCenter: center}"
			@close="closePopup"
		>
			<slot></slot>
			<span v-if="footer" slot="footer" class="dialog-footer">
				<el-button v-if="isShowClose" class="dt-btn" type="primary" plain :style="{color:$store.state.layoutStore.themeObj.color}" @click="show = false">{{closeText}}</el-button>
				<el-button type="primary" class="dt-btn" @click="confirm">{{confirmText}}</el-button>
			</span>
			<span v-if="isShowClose" class="iconfont icondt25" style="cursor: pointer;" @click="show = false"></span>
		</el-dialog>
	</div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "系统提示"
    },
    isShow: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: "670px"
    },
    footer: {
      type: Boolean,
      default: true
    },
    center: {
      type: Boolean,
      default: false
    },
    isCenter: {
      type: Boolean,
      default: true
    },
    isShowClose: {
      type: Boolean,
      default: true
    },
	closeText:{
    	type:String,
		default:"取 消"
	},
  	confirmText:{
		type:String,
		default:"确 认"
	}
  },
  data() {
    return {
      show: this.isShow
    };
  },
  watch:{
    isShow(newVal,oldVal) {
      this.show = newVal;
      if(this.show){
        this.$emit('open')
      }
    }
  },
  methods: {
    closePopup() {
      this.$emit("close");
    },
    confirm() {
       this.$emit("confirm",()=>{
            this.show = false;
        });
    }
  }
};
</script>

<style lang="less">
.dt-popup {
  .el-dialog {
    min-width: 300px;
    min-height: 150px;
    border-radius: 10px;
    position: absolute;
    margin: 0!important;
    width: auto;
    padding: 0 40px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .el-dialog__body {
      padding: 0;
      .dt-popup-wrap{
          max-height:55vh;
          overflow-y: auto;
          width:calc(~'100% - 10px');
          padding-right:10px;
      }
    }
    .el-dialog__header {
      padding-bottom: 20px;
    }
    .el-dialog__footer {
      padding-bottom: 40px;
      padding-top: 20px;
    }
  }
  &.isCenter {
    .el-dialog__body {
      display: flex;
      justify-content: center;
    }
  }
  &.small {
    .el-dialog {
      width: 700px;
    }
  }
  &.mini {
    .el-dialog {
      width: 400px;
    }
  }
  &.large {
    .el-dialog {
      width: 1000px;
    }
  }
  .icondt25 {
    position: absolute;
    font-size: 65px !important;
    top: -12px ;
    right: -11px ;
    color: #B8B8B8;
  }
  .dialog-footer {

  }
  .el-dialog__title{
    font-weight: bold;
  }
}
</style>
