export default [
  {
    path: '/enterprise/type',
    name: 'enterpriseType',
    component: () => import('@/views/enterprise/type/index.vue'),
    meta: { title: '企业类型管理' }
  },
  {
    path: '/enterprise/type/edit/:id?',
    name: 'enterpriseTypeEdit',
    component: () => import('@/views/enterprise/type/edit.vue'),
    meta: { title: '编辑企业类型' }
  },

  {
    path: '/enterprise/info',
    name: 'EnterpriseManagement', // 更新为面包屑中使用的名称
    component: () => import('@/views/enterprise/info/index.vue'),
    meta: { title: '企业信息管理' }
  },
  {
    path: '/enterprise/customer',
    name: 'EnterpriseCustomer',
    component: () => import('@/views/enterprise/customer/index.vue'),
    meta: { title: '企业客户管理' }
  },
  {
    path: '/enterprise/customer/detail',
    name: 'EnterpriseCustomerDetail',
    component: () => import('@/views/enterprise/customer/detail.vue'),
    meta: { title: '企业详情' }
  },

  {
    path: '/enterprise/third-party-query/:id',
    name: 'thirdPartyQuery',
    component: () => import('@/views/enterprise/thirdPartyQuery.vue'),
    meta: { title: '企业信息查看' }
  },
  {
    path: '/enterprise/kyc-query/:id',
    name: 'kycQuery',
    component: () => import('@/views/enterprise/kycQuery.vue'),
    meta: { title: 'KYC信息查询' }
  },
  {
    path: '/enterprise/stats',
    name: 'enterpriseStats',
    component: () => import('@/views/enterprise/stats/index.vue'),
    meta: { title: '统计分析' }
  }
]
