import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'
import { getEnterpriseTypeOptions } from '@/api/enterprise/type'

// 获取风险矩阵列表
export const getRiskMatrixList = async (params = {}) => {
    return http.Axios.post(rootPath + '/api/elms/riskMatrix/page', params)
};

// 获取风险矩阵详情
export const getRiskMatrixDetail = async (id) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/riskMatrix/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('获取风险矩阵详情失败:', error)
    throw error
  }
};

// 保存风险矩阵（新增或更新）
export const saveRiskMatrix = async (data) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/riskMatrix/save', data)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('保存风险矩阵失败:', error)
    throw error
  }
};

// 删除风险矩阵
export const deleteRiskMatrix = async (id) => {
  try {
    const response = await http.Axios.delete(rootPath + `/api/elms/riskMatrix/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('删除风险矩阵失败:', error)
    throw error
  }
};

// 批量删除风险矩阵
export const batchDeleteRiskMatrix = async (ids) => {
  try {
    const response = await http.Axios.delete(rootPath + '/api/elms/riskMatrix/batch', { data: ids })
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('批量删除风险矩阵失败:', error)
    throw error
  }
};

// 获取评分项列表
export const getScoreItemList = async (params = {}) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/scoreItem/page', params)
    return response
  } catch (error) {
    console.error('获取评分项列表失败:', error)
    throw error
  }
};

// 获取评分项详情
export const getScoreItemDetail = async (id) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/scoreItem/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('获取评分项详情失败:', error)
    throw error
  }
};

// 保存评分项（新增或更新）
export const saveScoreItem = async (data) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/scoreItem/save', data)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('保存评分项失败:', error)
    throw error
  }
};

// 删除评分项
export const deleteScoreItem = async (id) => {
  try {
    const response = await http.Axios.delete(rootPath + `/api/elms/scoreItem/${id}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('删除评分项失败:', error)
    throw error
  }
};

// 批量删除评分项
export const batchDeleteScoreItem = async (ids) => {
  try {
    const response = await http.Axios.delete(rootPath + '/api/elms/scoreItem/batch', { data: ids })
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('批量删除评分项失败:', error)
    throw error
  }
};

// 根据类别查询评分项
export const getScoreItemByCategory = async (category) => {
  try {
    const response = await http.Axios.get(rootPath + `/api/elms/scoreItem/category/${category}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('根据类别查询评分项失败:', error)
    throw error
  }
};

// 根据ID列表查询评分项
export const getScoreItemByIds = async (ids) => {
  try {
    const response = await http.Axios.post(rootPath + '/api/elms/scoreItem/byIds', ids)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('根据ID列表查询评分项失败:', error)
    throw error
  }
};

// 检查风险矩阵编码是否存在
export const checkRiskMatrixCode = async (code, excludeId = null) => {
  try {
    const params = new URLSearchParams()
    params.append('code', code)
    if (excludeId) {
      params.append('excludeId', excludeId)
    }
    const response = await http.Axios.get(rootPath + `/api/elms/riskMatrix/checkCode?${params.toString()}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('检查风险矩阵编码失败:', error)
    throw error
  }
};

// 检查评分项编码是否存在
export const checkScoreItemCode = async (code, excludeId = null) => {
  try {
    const params = new URLSearchParams()
    params.append('code', code)
    if (excludeId) {
      params.append('excludeId', excludeId)
    }
    const response = await http.Axios.get(rootPath + `/api/elms/scoreItem/checkCode?${params.toString()}`)
    return {
      code: 200,
      message: 'success',
      data: response
    }
  } catch (error) {
    console.error('检查评分项编码失败:', error)
    throw error
  }
};

// 获取企业类型字典
export const getEnterpriseTypes = async () => {
  // 调用企业类型枚举接口获取真实数据
  const response = await getEnterpriseTypeOptions()

  // 确保返回的数据格式正确
  const data = response && Array.isArray(response) ? response : []

  return {
    code: 200,
    message: "success",
    data: data
  };
};

// 获取风险等级选项
export const getRiskLevelOptions = async () => {
  try {
    return {
      code: 200,
      message: "success",
      data: [
        { value: 'HIGH', label: '高' },
        { value: 'MEDIUM', label: '中' },
        { value: 'LOW', label: '低' }
      ]
    };
  } catch (error) {
    console.error('获取风险等级选项失败:', error)
    throw error
  }
};

// 保存风险矩阵类别配置
export const saveCategoriesForMatrix = async (matrixId, categories) => {
  try {
    const response = await http.Axios.post(rootPath + `/api/elms/riskMatrix/${matrixId}/categories`, categories)
    return response
  } catch (error) {
    console.error('保存类别配置失败:', error)
    throw error
  }
};

// 保存类别的档次配置
export const saveLevelsForCategory = async (matrixId, categoryId, levels) => {
  try {
    const response = await http.Axios.post(rootPath + `/api/elms/riskMatrix/${matrixId}/categories/${categoryId}/levels`, levels)
    return response
  } catch (error) {
    console.error('保存档次配置失败:', error)
    throw error
  }
};
