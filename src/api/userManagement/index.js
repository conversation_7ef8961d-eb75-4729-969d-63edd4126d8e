import http from "@/utils/httpService";
import {
  rootPath
} from "@/utils/globalParam";

// 用户列表分页接口
export const getUserList = (data) =>
  http.Axios.post(rootPath + "/api/user/page", data);

// 用户权限树接口
export const selectAuthTree = (data) =>
  http.Axios.post(rootPath + "/api/user/selectAuthTree", data);

// 根据云服账号获取用户信息
export const findByBscUserName = (data) =>
  http.Axios.post(rootPath + "/api/user/findByBscUserName", data);

// 用户详情
export const detail = (data) =>
    http.Axios.post(rootPath + "/api/user/detail", data);

// 用户增改
export const saveOrUpdate = (data) =>
  http.Axios.post(rootPath + "/api/user/saveOrUpdate", data);

// 删除用户
export const deleteUser = (data) =>
  http.Axios.post(rootPath + "/api/user/deleteUser", data);

// 获取行业
export const findIndustryList = (data) =>
  http.Axios.post(rootPath + "/api/industry/findIndustryList", data);

// 用户启用/禁用
export const setUserStatus = (data) =>
  http.Axios.post(rootPath + "/api/user/setUserStatus", data);

// 角色权限配置
export const saveOrUpdateRoleAuth = (data) =>
  http.Axios.post(rootPath + "/api/user/saveOrUpdateRoleAuth", data);

// 分配角色
export const addUserRole = (data) =>
  http.Axios.post(rootPath + "/api/user/addUserRole", data);

// 查用户所属角色
export const findRolesByUserId = (data) =>
  http.Axios.post(rootPath + "/api/user/findRolesByUserId", data);

// 删除角色
export const deleteUserRole = (data) =>
  http.Axios.post(rootPath + "/api/user/deleteUserRole", data);

// 租户下所有角色
export const getRoleList = (data) =>
  http.Axios.post(rootPath + "/api/role/list", data);

// 总部树结构数据查询
export const findDepartmentData = (data) =>
  http.Axios.post(rootPath + "/api/user/findDepartmentData", data);

// 分公司树结构数据查询
export const findOrganizationData = (data) =>
  http.Axios.post(rootPath + "/api/user/findOrganizationData", data);

// 法人机构列表数据
export const findLegalOrgData = (data) =>
  http.Axios.post(rootPath + "/api/user/findLegalOrgData", data);

// 机构权限配置
export const saveOrUpdateUserOrg = (data) =>
  http.Axios.post(rootPath + "/api/user/saveOrUpdateUserOrg", data);

// 获取用户选中的机构权限
export const getCheckedUserOrg = (data) =>
  http.Axios.post(rootPath + "/api/user/getCheckedUserOrg", data);
