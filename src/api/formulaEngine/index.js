import http from "@/utils/httpService";
import { rootPath } from '@/utils/globalParam';

/**
 * 公式管理 API
 */

// 分页查询公式列表
export const getFormulaList = (pageRequest) => {
  return http.Axios.post(rootPath + "/api/formula/list", pageRequest);
};

// 获取公式详情
export const getFormulaDetail = (id) => {
  return http.Axios.get(rootPath + `/api/formula/${id}`);
};

// 创建公式
export const createFormula = (data) => {
  return http.Axios.post(rootPath + "/api/formula", data);
};

// 更新公式
export const updateFormula = (id, data) => {
  return http.Axios.put(rootPath + `/api/formula/${id}`, data);
};

// 删除公式
export const deleteFormula = (id) => {
  return http.Axios.delete(rootPath + `/api/formula/${id}`);
};

// 批量删除公式
export const batchDeleteFormula = (ids) => {
  return http.Axios.delete(rootPath + "/api/formula/batch", { data: ids });
};

// 更新公式状态
export const updateFormulaStatus = (id, status) => {
  return http.Axios.put(rootPath + `/api/formula/${id}/status?status=${status}`);
};

// 计算公式
export const calculateFormula = (data) => {
  return http.Axios.post(rootPath + "/api/formula/calculate", data);
};

// 验证公式语法
export const validateFormula = (formula) => {
  return http.Axios.post(rootPath + `/api/formula/validate?formula=${encodeURIComponent(formula)}`);
};

// 获取公式计算记录
export const getFormulaCalculationLogs = (id, limit = 10) => {
  return http.Axios.get(rootPath + `/api/formula/${id}/calculation-logs?limit=${limit}`);
};

// 获取公式分类选项
export const getFormulaCategoryOptions = () => {
  return http.Axios.get(rootPath + "/api/formula/categories");
};


// 获取状态选项
export const getStatusOptions = () => {
  return http.Axios.get(rootPath + "/api/formula/status-options");
};

// 获取变量类型选项
export const getVariableTypeOptions = () => {
  return http.Axios.get(rootPath + "/api/formula/variable-type-options");
};

// 获取常数列表（从常数管理模块获取）
export const getConstants = () => {
  return http.Axios.get(rootPath + "/api/constantConfig/enabled");
};
