<template>
  <EditPageContainer
    title="配置题目"
    icon="el-icon-menu"
    :breadcrumb-items="breadcrumbItems"
    :loading="saving"
    @save="handleSave"
    @back="handleBack"
  >
    <!-- 问卷基本信息展示 -->
    <div class="questionnaire-info">
      <InfoCard title="问卷信息" icon="el-icon-document">
        <InfoItem label="问卷标题" :value="questionnaireInfo.title" />
        <InfoItem label="问卷描述" :value="questionnaireInfo.description" />
        <InfoItem label="问卷状态">
            <el-tag
              :type="getStatusTagType(questionnaireInfo.status)"
              size="small"
            >
              {{ getStatusText(questionnaireInfo.status) }}
            </el-tag>
        </InfoItem>
        <InfoItem label="适用企业类型">
            <div v-if="questionnaireInfo.enterpriseTypeList && questionnaireInfo.enterpriseTypeList.length > 0">
              <el-tag
                v-for="type in questionnaireInfo.enterpriseTypeList"
                :key="type"
                size="small"
                class="enterprise-type-tag"
                type="info"
              >
                {{ getEnterpriseTypeText(type) }}
              </el-tag>
            </div>
            <span v-else class="empty-text">全部类型</span>
        </InfoItem>
      </InfoCard>
    </div>

    <!-- 题目配置区域 -->
    <div class="questions-config-section">
      <UniversalTable
        title="题目配置"
        subtitle="配置问卷题目，支持添加、编辑、删除和排序操作"
        title-icon="el-icon-edit-outline"
        :table-data="questions"
        :loading="loading"
        :columns="questionColumns"
        :actions="questionActions"
        :action-column-width="200"
        :show-search-form="false"
        :show-pagination="false"
        add-button-text="添加题目"
        empty-title="暂无题目"
        empty-description="点击添加题目按钮开始创建"
        @add="addQuestion"
        @action-click="handleQuestionAction"
      >
        <!-- 题目类型列插槽 -->
        <template #type="{ row }">
          <el-tag
            :type="getQuestionTypeTagType(row.type)"
            size="small"
          >
            {{ getQuestionTypeText(row.type) }}
          </el-tag>
        </template>

        <!-- 选项数量列插槽 -->
        <template #optionCount="{ row }">
          <span v-if="row.type === 'text'">-</span>
          <span v-else>{{ row.options ? row.options.length : 0 }}个选项</span>
        </template>

        <!-- 排序列插槽 -->
        <template #sortOrder="{ row, index }">
          <div class="sort-column">
            <div class="sort-number">
              {{ row.sortOrder || (index + 1) }}
            </div>
            <div class="sort-controls">
              <el-button
                v-if="index > 0"
                size="mini"
                icon="el-icon-top"
                circle
                @click="moveQuestion(index, 'up')"
              />
              <el-button
                v-if="index < questions.length - 1"
                size="mini"
                icon="el-icon-bottom"
                circle
                @click="moveQuestion(index, 'down')"
              />
            </div>
          </div>
        </template>
      </UniversalTable>
    </div>

    <!-- 题目编辑弹窗 -->
    <el-dialog
      :visible.sync="questionDialogVisible"
      width="900px"
      :close-on-click-modal="false"
      custom-class="question-edit-dialog"
      :show-close="false"
      @close="resetQuestionForm"
    >
      <!-- 自定义弹窗头部 -->
      <div slot="title" class="dialog-header">
        <div class="header-left">
          <div class="header-title-row">
            <i class="el-icon-edit-outline header-icon"></i>
            <h3 class="dialog-title">{{ editingQuestion.id ? '编辑题目' : '添加题目' }}</h3>
          </div>
          <p class="dialog-subtitle">配置题目信息和选项内容</p>
        </div>
        <div class="header-actions">
          <el-button
            size="small"
            icon="el-icon-close"
            circle
            @click="questionDialogVisible = false"
            class="close-btn"
          />
        </div>
      </div>
      <!-- 弹窗内容区域 -->
      <div class="dialog-content">
        <!-- 基本信息区域 -->
        <div class="basic-info-section">
          <div class="section-title">
            <i class="el-icon-document"></i>
            <span>基本信息</span>
          </div>
          <UniversalForm
            ref="questionForm"
            :form-data="editingQuestion"
            :form-rules="questionFormRules"
            :form-groups="questionFormGroups"
            label-width="120px"
          />
        </div>

        <!-- 选项配置（仅选择题显示） -->
        <div v-if="editingQuestion.type !== 'text'" class="options-section">
          <div class="section-title-with-action">
            <div class="section-title">
              <i class="el-icon-menu"></i>
              <span>选项配置</span>
              <div class="title-badge">
                <span>{{ getQuestionTypeText(editingQuestion.type) }}</span>
              </div>
            </div>
            <el-button size="small" type="primary" @click="addOption" class="add-option-btn">
              <i class="el-icon-plus"></i>
              添加选项
            </el-button>
          </div>

        <!-- 选项表头 -->
        <div class="options-header">
          <div class="header-item index-header">序号</div>
          <div class="header-item content-header">内容</div>
          <div class="header-item score-header">分数</div>
          <div class="header-item action-header">操作</div>
        </div>

        <div class="options-list">
          <div
            v-for="(option, index) in editingQuestion.options"
            :key="option.id || index"
            class="option-item"
          >
            <div class="option-content">
              <div class="option-index">
                <span class="index-badge">{{ getOptionLabel(index) }}</span>
              </div>
              <el-input
                v-model="option.text"
                placeholder="请输入选项内容"
                class="option-text-input"
              />
              <el-input-number
                v-model="option.score"
                :min="0"
                :max="100"
                placeholder="分值"
                class="option-score-input"
                controls-position="right"
              />
              <div class="option-actions">
                <el-tooltip content="删除选项" placement="top">
                  <el-button
                    size="small"
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    @click="removeOption(index)"
                  />
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-if="editingQuestion.options.length === 0" class="empty-options">
          <i class="el-icon-info"></i>
          <span>暂无选项，点击上方"添加选项"按钮开始创建</span>
        </div>
      </div>

      <!-- 自定义弹窗底部 -->
      <div slot="footer" class="dialog-footer">
        <div class="footer-content">
          <div class="footer-info">
            <i class="el-icon-info"></i>
            <span v-if="editingQuestion.type !== 'text'">
              已配置 {{ editingQuestion.options.length }} 个选项
            </span>
            <span v-else>简答题无需配置选项</span>
          </div>
          <div class="footer-actions">
            <el-button @click="questionDialogVisible = false" class="cancel-btn">
              <i class="el-icon-close"></i>
              取消
            </el-button>
            <el-button type="primary" @click="saveQuestion" class="save-btn">
              <i class="el-icon-check"></i>
              {{ editingQuestion.id ? '更新题目' : '保存题目' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog
      ref="confirmDialog"
      title="删除确认"
      message="删除后无法恢复，请确认是否删除该题目？"
      icon="el-icon-warning"
      confirm-text="删除"
      cancel-text="取消"
      @confirm="confirmDeleteQuestion"
    />
  </EditPageContainer>
</template>

<script>
import { getQuestionnaireDetail, updateQuestionnaire } from '@/api/questionnaire/management'
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalTable from '@/components/layouts/UniversalTable.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import InfoCard from '@/components/layouts/InfoCard.vue'
import InfoItem from '@/components/layouts/InfoItem.vue'
import ConfirmDialog from '@/components/layouts/ConfirmDialog.vue'

export default {
  name: 'QuestionConfig',
  components: {
    EditPageContainer,
    UniversalTable,
    UniversalForm,
    InfoCard,
    InfoItem,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      saving: false,
      questionnaireInfo: {
        id: null,
        title: '',
        description: '',
        status: 0,
        statusDesc: '',
        enterpriseTypes: '', // 原始字符串
        enterpriseTypeList: [] // 企业类型列表
      },
      questions: [],
      questionDialogVisible: false,
      editingQuestion: {
        id: null,
        type: 'single',
        title: '',
        scoreItem: '',
        options: []
      },
      deleteQuestionIndex: -1,
      scoreItemOptions: [
        { value: '财务风险评分', label: '财务风险评分' },
        { value: '运营风险评分', label: '运营风险评分' },
        { value: '市场风险评分', label: '市场风险评分' },
        { value: '合规风险评分', label: '合规风险评分' },
        { value: '技术风险评分', label: '技术风险评分' },
        { value: '人员风险评分', label: '人员风险评分' }
      ]
    }
  },
  computed: {
    breadcrumbItems() {
      return [
        { text: '问卷管理', to: '/questionnaire' },
        { text: '配置题目' }
      ]
    },
    questionColumns() {
      return [
        { prop: 'title', label: '题目内容', minWidth: 300 },
        { prop: 'type', label: '题目类型', width: 120, slot: true, align: 'center' },
        { prop: 'scoreItem', label: '评分项', width: 150 },
        { prop: 'optionCount', label: '选项数量', width: 120, slot: true, align: 'center' },
        { prop: 'sortOrder', label: '排序', width: 100, slot: true, align: 'center' }
      ]
    },
    questionActions() {
      return [
        { key: 'edit', label: '编辑', icon: 'el-icon-edit', class: 'edit-btn', size: 'mini' },
        { key: 'delete', label: '删除', icon: 'el-icon-delete', class: 'delete-btn', size: 'mini' }
      ]
    },
    questionFormRules() {
      return {
        type: [{ required: true, message: '请选择题型', trigger: 'change' }],
        title: [{ required: true, message: '请输入题目内容', trigger: 'blur' }]
      }
    },
    questionFormGroups() {
      return [
        {
          title: '题目信息',
          fields: [[
            {
              prop: 'type',
              label: '题型',
              type: 'select',
              options: [
                { value: 'single', label: '单选题' },
                { value: 'multi', label: '多选题' },
                { value: 'text', label: '简答题' }
              ],
              required: true
            },
            {
              prop: 'scoreItem',
              label: '关联评分项',
              type: 'select',
              options: [{ label: '不关联评分项', value: '' }, ...this.scoreItemOptions],
              clearable: true
            },
            {
              prop: 'title',
              label: '题目内容',
              type: 'textarea',
              placeholder: '请输入题目内容',
              required: true
            }
          ]]
        }
      ]
    }
  },
  created() {
    this.loadQuestionnaireData()
  },
  methods: {
    // 加载问卷数据
    async loadQuestionnaireData() {
      const questionnaireId = this.$route.params.id
      if (!questionnaireId) {
        this.$message.error('问卷ID不能为空')
        this.$router.back()
        return
      }

      this.loading = true
      try {
        const response = await getQuestionnaireDetail(questionnaireId)
        if (response) {
          console.log('问卷详情数据:', response) // 调试日志
          console.log('企业类型字符串:', response.enterpriseTypes)
          console.log('企业类型列表:', response.enterpriseTypeList)

          this.questionnaireInfo = {
            id: response.id,
            title: response.title,
            description: response.description,
            status: response.status,
            statusDesc: response.statusDesc,
            enterpriseTypes: response.enterpriseTypes, // 保留原始字符串
            enterpriseTypeList: response.enterpriseTypeList || [] // 使用后端返回的列表
          }

          console.log('设置后的问卷信息:', this.questionnaireInfo) // 调试日志

          this.questions = (response.questions || []).map(q => ({
            id: q.id,
            type: q.type,
            title: q.title,
            scoreItem: q.scoreItem,
            sortOrder: q.sortOrder || 0, // 添加排序字段
            options: (q.options || []).map(opt => ({
              id: opt.id,
              text: opt.optionText || opt.text, // 兼容两种字段名
              score: opt.score || 0
            }))
          }))
        }
      } catch (error) {
        console.error('加载问卷数据失败:', error)
        this.$message.error('加载问卷数据失败')
      } finally {
        this.loading = false
      }
    },

    // 添加题目
    addQuestion() {
      this.editingQuestion = {
        id: null,
        type: 'single',
        title: '',
        scoreItem: '',
        options: []
      }
      this.questionDialogVisible = true
    },

    // 编辑题目
    editQuestion(question, index) {
      this.editingQuestion = {
        ...JSON.parse(JSON.stringify(question)),
        _index: index
      }
      this.questionDialogVisible = true
    },

    // 删除题目
    deleteQuestion(index) {
      this.deleteQuestionIndex = index
      this.$refs.confirmDialog.show()
    },

    // 确认删除题目
    confirmDeleteQuestion() {
      if (this.deleteQuestionIndex >= 0) {
        this.questions.splice(this.deleteQuestionIndex, 1)
        this.$message.success('题目删除成功')
        this.deleteQuestionIndex = -1
      }
    },

    // 移动题目位置
    moveQuestion(index, direction) {
      const newIndex = direction === 'up' ? index - 1 : index + 1
      if (newIndex >= 0 && newIndex < this.questions.length) {
        const temp = this.questions[index]
        this.$set(this.questions, index, this.questions[newIndex])
        this.$set(this.questions, newIndex, temp)

        // 更新所有题目的排序号
        this.updateQuestionSortOrder()

        this.$message.success('题目顺序调整成功')
      }
    },

    // 更新题目排序号
    updateQuestionSortOrder() {
      this.questions.forEach((question, index) => {
        this.$set(question, 'sortOrder', index + 1)
      })
    },

    // 题目操作处理
    handleQuestionAction(eventData) {
      const { action, row, index } = eventData
      switch (action) {
        case 'edit':
          this.editQuestion(row, index)
          break
        case 'delete':
          this.deleteQuestion(index)
          break
      }
    },

    // 保存题目
    async saveQuestion() {
      try {
        await this.$refs.questionForm.validate()

        // 验证选择题必须有选项
        if (this.editingQuestion.type !== 'text' && this.editingQuestion.options.length === 0) {
          this.$message.warning('请至少添加一个选项')
          return
        }

        // 验证选项内容
        if (this.editingQuestion.type !== 'text') {
          const emptyOptions = this.editingQuestion.options.filter(opt => !opt.text || opt.text.trim() === '')
          if (emptyOptions.length > 0) {
            this.$message.warning('请完善所有选项内容')
            return
          }
        }

        const questionData = {
          ...this.editingQuestion,
          id: this.editingQuestion.id || Date.now()
        }

        if (this.editingQuestion._index !== undefined) {
          // 编辑模式 - 保持原有的排序号
          questionData.sortOrder = this.questions[this.editingQuestion._index].sortOrder
          this.$set(this.questions, this.editingQuestion._index, questionData)
          this.$message.success('题目更新成功')
        } else {
          // 新增模式 - 设置为最后一个位置
          questionData.sortOrder = this.questions.length + 1
          this.questions.push(questionData)
          this.$message.success('题目添加成功')
        }

        this.questionDialogVisible = false
      } catch (error) {
        this.$message.warning('请完善题目信息')
      }
    },

    // 重置题目表单
    resetQuestionForm() {
      this.editingQuestion = {
        id: null,
        type: 'single',
        title: '',
        scoreItem: '',
        options: []
      }
    },

    // 添加选项
    addOption() {
      const optionCount = this.editingQuestion.options.length
      this.editingQuestion.options.push({
        id: Date.now(),
        text: '',
        score: optionCount + 1 // 默认分值递增
      })

      // 自动聚焦到新添加的选项输入框
      this.$nextTick(() => {
        const inputs = this.$el.querySelectorAll('.option-text-input input')
        if (inputs.length > 0) {
          inputs[inputs.length - 1].focus()
        }
      })
    },

    // 删除选项
    removeOption(index) {
      this.editingQuestion.options.splice(index, 1)
    },

    // 获取题目类型标签类型
    getQuestionTypeTagType(type) {
      const typeMap = {
        single: 'primary',
        multi: 'success',
        text: 'info'
      }
      return typeMap[type] || 'info'
    },

    // 获取题目类型文本
    getQuestionTypeText(type) {
      const typeMap = {
        single: '单选题',
        multi: '多选题',
        text: '简答题'
      }
      return typeMap[type] || '未知'
    },

    // 获取选项标签（A、B、C...）
    getOptionLabel(index) {
      return String.fromCharCode(65 + index) // A=65, B=66, C=67...
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        0: 'danger',  // 禁用
        1: 'success', // 启用
        2: 'warning'  // 草稿
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '禁用',
        1: '启用',
        2: '草稿'
      }
      return statusMap[status] || '未知'
    },

    // 获取企业类型文本
    getEnterpriseTypeText(typeCode) {
      const typeMap = {
        'A': 'A类',
        'B': 'B类',
        'C': 'C类',
        'D': 'D类',
        'E': 'E类'
      }
      return typeMap[typeCode] || typeCode
    },

    // 保存配置
    async handleSave() {
      if (this.questions.length === 0) {
        this.$message.warning('请至少添加一个题目')
        return
      }

      this.saving = true
      try {
        const saveData = {
          id: this.questionnaireInfo.id,
          title: this.questionnaireInfo.title,
          description: this.questionnaireInfo.description,
          enterpriseTypes: this.questionnaireInfo.enterpriseTypeList || [], // 使用列表格式
          status: this.questionnaireInfo.status,
          questions: this.questions.map((question, index) => {
            const questionData = {
              id: question.id && question.id < Date.now() - 1000000 ? question.id : null, // 只有真实的ID才传递
              title: question.title,
              type: question.type,
              scoreItem: question.scoreItem || null,
              maxScore: 100, // 默认最高分
              required: 1, // 默认必填
              sortOrder: index + 1 // 使用sortOrder而不是sort
            }

            // 只有选择题才需要选项
            if (question.type !== 'text' && question.options && question.options.length > 0) {
              questionData.options = question.options.map((option, optionIndex) => ({
                id: option.id && typeof option.id === 'number' && option.id < Date.now() - 1000000 ? option.id : null, // 只有真实的ID才传递
                optionText: option.text || '', // 使用optionText而不是text
                optionValue: option.text || '', // optionValue也是必填字段，使用相同的值
                score: option.score || 0,
                sortOrder: optionIndex + 1
              }))
            } else {
              questionData.options = []
            }

            return questionData
          })
        }
        console.log('保存的数据:', saveData)

        const response = await updateQuestionnaire(this.questionnaireInfo.id, saveData)
        if (response) {
          this.$message.success('题目配置保存成功')
          this.$router.push('/questionnaire')
        }
      } catch (error) {
        console.error('保存失败:', error)
        // 显示更详细的错误信息
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(`保存失败：${error.response.data.message}`)
        } else {
          this.$message.error('保存失败，请重试')
        }
      } finally {
        this.saving = false
      }
    },

    // 返回
    handleBack() {
      this.$router.back()
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../assets/style/shared-styles.less";

.questionnaire-info {
  margin-bottom: 24px;
}

.enterprise-type-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.empty-text {
  color: #909399;
  font-style: italic;
}

.questions-config-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sort-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.sort-number {
  font-weight: bold;
  color: #409EFF;
  font-size: 16px;
  min-width: 20px;
  text-align: center;
}

.sort-controls {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.options-section {
  margin-top: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.header-subtitle {
  font-size: 12px;
  color: #909399;
  margin: 0;
}

.options-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #e9ecef;
  border-radius: 6px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.header-item {
  display: flex;
  align-items: center;

  &.index-header {
    width: 60px;
    justify-content: center;
  }

  &.content-header {
    flex: 1;
  }

  &.score-header {
    width: 120px;
    justify-content: center;
  }

  &.action-header {
    width: 60px;
    justify-content: center;
  }
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }
}

.option-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-index {
  width: 60px;
  display: flex;
  justify-content: center;
}

.index-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 600;
}

.option-text-input {
  flex: 1;
}

.option-score-input {
  width: 120px;
}

.option-actions {
  width: 60px;
  display: flex;
  justify-content: center;
}

.empty-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;

  i {
    font-size: 48px;
    margin-bottom: 12px;
    color: #c0c4cc;
  }

  span {
    text-align: center;
    line-height: 1.5;
  }
}

// 题目编辑弹窗样式优化
/deep/ .question-edit-dialog {
  border-radius: 12px;
  overflow: hidden;

  .el-dialog__header {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 0;
    border-top: 1px solid #f0f0f0;
  }

  // 关联评分项选择框宽度调整
  .el-form .el-row:nth-child(1) .el-col:nth-child(2) {
    .el-select,
    .el-input,
    .el-input__inner {
      width: 270px !important;
    }
  }
}

// 弹窗头部样式
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
  color: white;

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .header-title-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon {
    font-size: 24px;
    color: white;
    opacity: 0.9;
  }

  .dialog-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.2;
    color: white;
  }

  .dialog-subtitle {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
    line-height: 1.2;
    color: white;
    margin-left: 36px; // 与标题对齐（图标宽度24px + 间距12px）
  }

  .close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      color: white;
    }
  }
}

// 弹窗内容区域
.dialog-content {
  padding: 24px;
}

// 基本信息区域
.basic-info-section {
  margin-bottom: 24px;
}

// 区域标题样式
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;

  i {
    font-size: 18px;
    color: #D7A256;
  }

  .title-badge {
    margin-left: 8px;

    span {
      display: inline-block;
      padding: 2px 8px;
      background: rgba(215, 162, 86, 0.1);
      color: #D7A256;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// 带操作按钮的标题
.section-title-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .add-option-btn {
    background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
    border: none;
    border-radius: 6px;
    font-weight: 500;

    &:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }
  }
}

// 弹窗底部样式
.dialog-footer {
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fafbfc;
  }

  .footer-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #606266;
    font-size: 14px;

    i {
      color: #909399;
    }
  }

  .footer-actions {
    display: flex;
    gap: 12px;

    .cancel-btn {
      color: #606266;
      border-color: #dcdfe6;

      &:hover {
        color: #D7A256;
        border-color: #D7A256;
      }
    }

    .save-btn {
      background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
      border: none;
      font-weight: 500;

      &:hover {
        opacity: 0.9;
        transform: translateY(-1px);
      }
    }
  }
}

// 选项配置区域优化
.options-section {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-top: 0;

  .options-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    font-weight: 600;
    color: #495057;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .option-item {
    background: #fafbfc;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #D7A256;
      box-shadow: 0 2px 12px rgba(215, 162, 86, 0.15);
      transform: translateY(-1px);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .index-badge {
    background: linear-gradient(135deg, #D7A256 0%, #E6B366 100%);
    box-shadow: 0 2px 4px rgba(215, 162, 86, 0.3);
    font-weight: 600;

    &:hover {
      transform: scale(1.05);
    }
  }

  .empty-options {
    text-align: center;
    padding: 48px 20px;
    color: #909399;
    background: #fafbfc;
    border: 2px dashed #e4e7ed;
    border-radius: 8px;

    i {
      font-size: 56px;
      margin-bottom: 16px;
      color: #c0c4cc;
      display: block;
    }

    span {
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

// 输入框和按钮优化
/deep/ .question-edit-dialog {
  .option-text-input {
    .el-input__inner {
      border-radius: 6px;
      border-color: #e4e7ed;

      &:focus {
        border-color: #D7A256;
        box-shadow: 0 0 0 2px rgba(215, 162, 86, 0.1);
      }
    }
  }

  .option-score-input {
    .el-input-number__decrease,
    .el-input-number__increase {
      border-radius: 4px;
    }

    .el-input__inner {
      border-radius: 6px;
      text-align: center;
    }
  }

  .option-actions {
    .el-button {
      &.is-circle {
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  /deep/ .question-edit-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .dialog-header {
    padding: 16px 20px;

    .header-icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }

    .dialog-title {
      font-size: 18px;
    }
  }

  .dialog-content {
    padding: 20px;
  }

  .options-section {
    padding: 16px;
  }

  .option-content {
    flex-direction: column;
    gap: 12px;

    .option-index,
    .option-text-input,
    .option-score-input,
    .option-actions {
      width: 100%;
    }

    .option-actions {
      display: flex;
      justify-content: center;
    }
  }
}
</style>
