<template>
  <div class="enterprise-customer-container">
    <!-- 使用通用表格组件 -->
    <UniversalTable
      title="企业客户管理"
      subtitle="企业客户统计信息，包含企业客户数量、机会数量等统计数据"
      title-icon="el-icon-office-building"
      :table-data="tableData"
      :loading="loading"
      :columns="tableColumns"
      :search-form-config="searchFormConfig"
      :search-params="searchParamsWithParam"
      :pagination-data="pagination"
      :total="pagination.total"
      :search-label-width="'100px'"
      :show-add-button="false"
      empty-title="暂无企业客户数据"
      empty-description="暂无符合条件的企业客户统计信息"
      @search="handleSearch"
      @reset="handleReset"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </UniversalTable>
  </div>
</template>

<script>
import UniversalTable from '@/components/layouts/UniversalTable'
import { statAgentEnterprise } from '@/api/agentEnterprise'

export default {
  name: 'EnterpriseCustomerList',
  components: {
    UniversalTable
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      searchParams: {
        agentName: '',
        agentCode: ''
      },

      // 搜索表单配置
      searchFormConfig: [
        {
          type: 'input',
          prop: 'agentName',
          label: '姓名',
          placeholder: '请输入姓名'
        },
        {
          type: 'input',
          prop: 'agentCode',
          label: '工号',
          placeholder: '请输入工号'
        }
      ],

      // 表格列配置
      tableColumns: [
        {
          prop: 'agentName',
          label: '姓名',
          width: 120,
          align: 'center'
        },
        {
          prop: 'agentCode',
          label: '工号',
          width: 120,
          align: 'center'
        },
        {
          prop: 'legalName',
          label: '机构',
          minWidth: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'salesCenterName',
          label: '营业部',
          minWidth: 180,
          showOverflowTooltip: true
        },
        {
          prop: 'agentEnterpriseNum',
          label: '企业客户数量',
          width: 130,
          align: 'center'
        },
        {
          prop: 'verifiedEnterpriseNum',
          label: '已验真企业数量',
          width: 150,
          align: 'center'
        },
        {
          prop: 'opportunityNum',
          label: '机会数量',
          width: 100,
          align: 'center'
        },
        {
          prop: 'emplayeeOpportunityNum',
          label: '员福机会数量',
          width: 130,
          align: 'center'
        },
        {
          prop: 'generalOpportunityNum',
          label: '综合保障机会数量',
          width: 160,
          align: 'center'
        }
      ]
    }
  },

  computed: {
    searchParamsWithParam() {
      return {
        ...this.searchParams,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
    }
  },

  mounted() {
    this.loadData()
  },

  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          param: {
            ...this.searchParams
          }
        }

        const response = await statAgentEnterprise(params)
        console.log('企业客户数据:', response)
        if (response && response.list) {
          this.tableData = response.list || []
          this.pagination.total = response.total || 0
        }
      } catch (error) {
        console.error('加载企业客户数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch(searchParams) {
      this.searchParams = { ...searchParams }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchParams = {
        agentName: '',
        agentCode: ''
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.pageNum = page
      this.loadData()
    }
  }
}
</script>

<style lang="less" scoped>
.enterprise-customer-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}
</style>
