package com.kbao.kbcelms.formula.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.formula.dao.FormulaCalculationLogMapper;
import com.kbao.kbcelms.formula.entity.FormulaCalculationLog;
import com.kbao.kbcelms.formula.vo.FormulaCalculationResultVO;
import com.kbao.kbcelms.formula.vo.FormulaVO;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 公式计算服务
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class FormulaCalculationService extends BaseSQLServiceImpl<FormulaCalculationLog, Long, FormulaCalculationLogMapper> {

    private final ObjectMapper objectMapper;
    private final ScriptEngineManager scriptEngineManager;

    // 安全的数学函数正则表达式
    private static final Pattern SAFE_FORMULA_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+\\-*/().\\s,<>=!&|^%]+$"
    );

    public FormulaCalculationService() {
        this.objectMapper = new ObjectMapper();
        this.scriptEngineManager = new ScriptEngineManager();
    }

    /**
     * 计算公式
     *
     * @param formulaVO 公式信息
     * @param variables 输入变量
     * @return 计算结果
     */
    public FormulaCalculationResultVO calculate(FormulaVO formulaVO, Map<String, BigDecimal> variables) {
        log.info("开始计算公式 - formulaId: {}, name: {}", formulaVO.getId(), formulaVO.getName());

        long startTime = System.currentTimeMillis();
        FormulaCalculationResultVO result = new FormulaCalculationResultVO();
        result.setFormulaId(formulaVO.getId());
        result.setFormulaName(formulaVO.getName());
        result.setFormula(formulaVO.getFormula());
        result.setInputVariables(variables);
        result.setCalculationTime(LocalDateTime.now());

        try {
            // 验证公式安全性
            if (!validateSafety(formulaVO.getFormula())) {
                throw new RuntimeException("公式包含不安全的内容");
            }

            // 替换变量
            String processedFormula = replaceVariables(formulaVO.getFormula(), variables);
            log.debug("处理后的公式: {}", processedFormula);

            // 执行计算
            BigDecimal calculationResult = executeCalculation(processedFormula);
            
            result.setResult(calculationResult);
            result.setStatus(1);

            log.info("公式计算成功 - result: {}", calculationResult);

        } catch (Exception e) {
            log.error("公式计算失败 - formulaId: {}, error: {}", formulaVO.getId(), e.getMessage(), e);
            result.setStatus(0);
            result.setErrorMessage(e.getMessage());
        }

        // 计算执行时间
        long endTime = System.currentTimeMillis();
        result.setExecutionTimeMs((int) (endTime - startTime));

        // 保存计算记录
        saveCalculationLog(result);

        return result;
    }

    /**
     * 验证公式语法
     *
     * @param formula 公式内容
     * @return 是否有效
     */
    public Boolean validateSyntax(String formula) {
        log.info("验证公式语法 - formula: {}", formula);

        if (!StringUtils.hasText(formula)) {
            throw new RuntimeException("公式内容不能为空");
        }

        try {
            // 验证安全性
            if (!validateSafety(formula)) {
                throw new RuntimeException("公式包含不安全的内容");
            }

            // 尝试解析公式（使用默认变量值）
            String testFormula = replaceVariablesWithDefaults(formula);
            log.info("原始公式: {}", formula);
            log.info("测试公式: {}", testFormula);

            // 进行简单的语法检查，而不是完整执行
            if (isValidJavaScriptSyntax(testFormula)) {
                log.info("公式语法验证通过");
                return true;
            } else {
                throw new RuntimeException("公式语法不正确");
            }

        } catch (Exception e) {
            log.error("公式语法验证失败 - error: {}", e.getMessage());
            throw new RuntimeException("公式语法错误: " + e.getMessage());
        }
    }

    /**
     * 检查是否为有效的JavaScript语法
     *
     * @param formula 处理后的公式
     * @return 是否有效
     */
    private boolean isValidJavaScriptSyntax(String formula) {
        try {
            ScriptEngine engine = scriptEngineManager.getEngineByName("JavaScript");
            if (engine == null) {
                return false;
            }

            // 添加数学函数支持
            engine.eval("function factorial(n) { return n <= 1 ? 1 : n * factorial(n - 1); }");

            // 只检查语法，不执行计算
            engine.eval("var testResult = " + formula + ";");
            return true;

        } catch (Exception e) {
            log.warn("JavaScript语法检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证公式安全性
     *
     * @param formula 公式内容
     * @return 是否安全
     */
    private Boolean validateSafety(String formula) {
        if (!StringUtils.hasText(formula)) {
            return false;
        }

        // 检查是否包含危险关键字
        String[] dangerousKeywords = {
                "import", "class", "new", "Runtime", "Process", "System", "File", 
                "Thread", "exec", "eval", "function", "var", "let", "const"
        };

        String lowerFormula = formula.toLowerCase();
        for (String keyword : dangerousKeywords) {
            if (lowerFormula.contains(keyword.toLowerCase())) {
                log.warn("公式包含危险关键字: {}", keyword);
                return false;
            }
        }

        // 使用正则表达式验证字符安全性
        if (!SAFE_FORMULA_PATTERN.matcher(formula).matches()) {
            log.warn("公式包含不安全的字符");
            return false;
        }

        return true;
    }

    /**
     * 替换公式中的变量
     *
     * @param formula 原始公式
     * @param variables 变量值
     * @return 处理后的公式
     */
    private String replaceVariables(String formula, Map<String, BigDecimal> variables) {
        String result = formula;
        log.info("替换变量前的公式: {}", result);

        if (variables != null && !variables.isEmpty()) {
            // 先保护数学函数名，避免被变量替换
            result = result.replaceAll("\\bpow\\b", "MATHPOW");
            result = result.replaceAll("\\bsqrt\\b", "MATHSQRT");
            result = result.replaceAll("\\bsin\\b", "MATHSIN");
            result = result.replaceAll("\\bcos\\b", "MATHCOS");
            result = result.replaceAll("\\btan\\b", "MATHTAN");
            result = result.replaceAll("\\blog\\b", "MATHLOG");
            result = result.replaceAll("\\bln\\b", "MATHLN");
            result = result.replaceAll("\\bexp\\b", "MATHEXP");
            result = result.replaceAll("\\babs\\b", "MATHABS");
            result = result.replaceAll("\\bfactorial\\b", "FACTORIAL");
            log.info("保护函数名后: {}", result);

            // 替换用户变量
            for (Map.Entry<String, BigDecimal> entry : variables.entrySet()) {
                String variableName = entry.getKey();
                BigDecimal value = entry.getValue();

                if (value != null) {
                    // 替换变量名为具体数值
                    result = result.replaceAll("\\b" + variableName + "\\b", value.toString());
                }
            }
            log.info("替换用户变量后: {}", result);

            // 恢复数学函数名
            result = result.replaceAll("MATHPOW", "pow");
            result = result.replaceAll("MATHSQRT", "sqrt");
            result = result.replaceAll("MATHSIN", "sin");
            result = result.replaceAll("MATHCOS", "cos");
            result = result.replaceAll("MATHTAN", "tan");
            result = result.replaceAll("MATHLOG", "log");
            result = result.replaceAll("MATHLN", "ln");
            result = result.replaceAll("MATHEXP", "exp");
            result = result.replaceAll("MATHABS", "abs");
            result = result.replaceAll("FACTORIAL", "factorial");
            log.info("恢复函数名后: {}", result);
        }

        return result;
    }

    /**
     * 使用默认值替换变量（用于语法验证）
     *
     * @param formula 原始公式
     * @return 处理后的公式
     */
    private String replaceVariablesWithDefaults(String formula) {
        String result = formula;
        log.info("步骤0 - 原始公式: {}", result);

        // 先替换数学常量
        result = result.replaceAll("\\bPI\\b", "Math.PI");
        result = result.replaceAll("\\bE\\b", "Math.E");
        log.info("步骤1 - 替换常量后: {}", result);

        // 然后替换数学函数，使用唯一占位符
        result = result.replaceAll("\\bpow\\b", "MATHPOW");
        result = result.replaceAll("\\bsqrt\\b", "MATHSQRT");
        result = result.replaceAll("\\bsin\\b", "MATHSIN");
        result = result.replaceAll("\\bcos\\b", "MATHCOS");
        result = result.replaceAll("\\btan\\b", "MATHTAN");
        result = result.replaceAll("\\blog\\b", "MATHLOG");
        result = result.replaceAll("\\bln\\b", "MATHLN");
        result = result.replaceAll("\\bexp\\b", "MATHEXP");
        result = result.replaceAll("\\babs\\b", "MATHABS");
        result = result.replaceAll("\\bfactorial\\b", "FACTORIAL");
        log.info("步骤2 - 替换函数为占位符后: {}", result);

        // 替换所有剩余的变量为默认值，但要更加小心
        // 先检查是否有可能的问题模式
        if (result.contains("(") && result.contains(")")) {
            // 如果包含函数调用，需要更仔细地处理
            result = replaceVariablesSafely(result);
        } else {
            // 简单的表达式，直接替换
            result = result.replaceAll("\\b[a-zA-Z_][a-zA-Z0-9_]*\\b", "1.0");
        }
        log.info("步骤3 - 替换变量为1.0后: {}", result);

        // 处理幂运算符 ^ 转换为 Math.pow
        result = result.replaceAll("([^\\s]+)\\s*\\^\\s*([^\\s]+)", "Math.pow($1, $2)");
        log.info("步骤4 - 处理^运算符后: {}", result);

        // 恢复数学函数
        result = result.replaceAll("MATHPOW", "Math.pow");
        result = result.replaceAll("MATHSQRT", "Math.sqrt");
        result = result.replaceAll("MATHSIN", "Math.sin");
        result = result.replaceAll("MATHCOS", "Math.cos");
        result = result.replaceAll("MATHTAN", "Math.tan");
        result = result.replaceAll("MATHLOG", "Math.log");
        result = result.replaceAll("MATHLN", "Math.log");
        result = result.replaceAll("MATHEXP", "Math.exp");
        result = result.replaceAll("MATHABS", "Math.abs");
        result = result.replaceAll("FACTORIAL", "factorial");
        log.info("步骤5 - 恢复函数名后: {}", result);

        return result;
    }

    /**
     * 安全地替换变量，避免替换函数名
     *
     * @param formula 公式
     * @return 处理后的公式
     */
    private String replaceVariablesSafely(String formula) {
        String result = formula;

        // 定义已知的数学函数和常量，不应该被替换
        String[] protectedWords = {
            "Math", "PI", "E", "factorial",
            "MATHPOW", "MATHSQRT", "MATHSIN", "MATHCOS", "MATHTAN",
            "MATHLOG", "MATHLN", "MATHEXP", "MATHABS", "FACTORIAL"
        };

        // 使用正则表达式，只替换不在保护列表中的变量
        // 这个正则表达式会匹配单词边界的标识符，但排除保护的词
        String pattern = "\\b(?!(?:" + String.join("|", protectedWords) + ")\\b)[a-zA-Z_][a-zA-Z0-9_]*\\b";
        result = result.replaceAll(pattern, "1.0");

        return result;
    }

    /**
     * 执行计算
     *
     * @param formula 处理后的公式
     * @return 计算结果
     */
    private BigDecimal executeCalculation(String formula) throws ScriptException {
        ScriptEngine engine = scriptEngineManager.getEngineByName("JavaScript");
        
        if (engine == null) {
            throw new RuntimeException("JavaScript引擎不可用");
        }

        // 添加数学函数支持
        engine.eval("function factorial(n) { return n <= 1 ? 1 : n * factorial(n - 1); }");
        
        // 处理数学函数和常量
        String processedFormula = formula;

        // 先替换数学常量
        processedFormula = processedFormula.replaceAll("\\bPI\\b", "Math.PI");
        processedFormula = processedFormula.replaceAll("\\bE\\b", "Math.E");

        // 然后替换数学函数
        processedFormula = processedFormula.replaceAll("\\bpow\\b", "Math.pow");
        processedFormula = processedFormula.replaceAll("\\bsqrt\\b", "Math.sqrt");
        processedFormula = processedFormula.replaceAll("\\bsin\\b", "Math.sin");
        processedFormula = processedFormula.replaceAll("\\bcos\\b", "Math.cos");
        processedFormula = processedFormula.replaceAll("\\btan\\b", "Math.tan");
        processedFormula = processedFormula.replaceAll("\\blog\\b", "Math.log");
        processedFormula = processedFormula.replaceAll("\\bln\\b", "Math.log");
        processedFormula = processedFormula.replaceAll("\\bexp\\b", "Math.exp");
        processedFormula = processedFormula.replaceAll("\\babs\\b", "Math.abs");

        // 处理幂运算符 ^ 转换为 Math.pow
        processedFormula = processedFormula.replaceAll("([^\\s]+)\\s*\\^\\s*([^\\s]+)", "Math.pow($1, $2)");

        log.info("最终执行的公式: {}", processedFormula);

        // 执行计算
        Object result = engine.eval(processedFormula);
        
        if (result instanceof Number) {
            return new BigDecimal(result.toString());
        } else {
            throw new RuntimeException("计算结果不是数值类型: " + result);
        }
    }

    /**
     * 保存计算记录
     *
     * @param result 计算结果
     */
    private void saveCalculationLog(FormulaCalculationResultVO result) {
        try {
            FormulaCalculationLog log = new FormulaCalculationLog();
            log.setFormulaId(result.getFormulaId());
            log.setFormulaName(result.getFormulaName());
            log.setCalculationResult(result.getResult());
            log.setCalculationTime(result.getCalculationTime());
            log.setExecutionTimeMs(result.getExecutionTimeMs());
            log.setStatus(result.getStatus());
            log.setErrorMessage(result.getErrorMessage());
            log.setUserId(SysLoginUtils.getUserId());

            // 将输入变量转换为JSON字符串
            if (result.getInputVariables() != null) {
                String inputVariablesJson = objectMapper.writeValueAsString(result.getInputVariables());
                log.setInputVariables(inputVariablesJson);
            }

            this.mapper.insertSelective(log);

        } catch (JsonProcessingException e) {
            log.error("保存计算记录失败 - JSON转换错误: {}", e.getMessage());
        } catch (Exception e) {
            log.error("保存计算记录失败: {}", e.getMessage());
        }
    }
}
