package com.kbao.kbcelms.formula.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.formula.entity.Formula;
import com.kbao.kbcelms.formula.vo.FormulaVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公式Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface FormulaMapper extends BaseMapper<Formula, Long> {

    /**
     * 根据编码查询公式
     *
     * @param name 公式名称
     * @return 公式
     */
    Formula selectByName(@Param("name") String name);

    /**
     * 检查名称是否存在
     *
     * @param name 公式名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 分页查询公式列表
     *
     * @param name 公式名称
     * @param category 分类
     * @param enterpriseType 企业类型列表
     * @param status 状态
     * @param version 版本号
     * @return 公式列表
     */
    List<FormulaVO> selectFormulaList(
            @Param("name") String name,
            @Param("category") Integer category,
            @Param("enterpriseType") List<String> enterpriseType,
            @Param("status") Integer status,
            @Param("version") String version
    );

    /**
     * 根据ID查询公式详情
     *
     * @param id 公式ID
     * @return 公式详情
     */
    FormulaVO selectFormulaDetail(@Param("id") Long id);

    /**
     * 更新使用次数
     *
     * @param id 公式ID
     * @return 影响行数
     */
    int updateUsageCount(@Param("id") Long id);

    /**
     * 更新最后测试时间
     *
     * @param id 公式ID
     * @return 影响行数
     */
    int updateLastTestTime(@Param("id") Long id);

    /**
     * 更新公式状态
     *
     * @param id 公式ID
     * @param status 状态
     * @return 影响行数
     */
    int updateFormulaStatus(@Param("id") Long id, @Param("status") Integer status);


}
