package com.kbao.kbcelms.enterprise.base.service;

import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.enterprise.base.dao.EnterpriseBeneficiaryDao;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBeneficiary;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 最终受益人信息业务逻辑层
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class EnterpriseBeneficiaryService extends BaseMongoServiceImpl<EnterpriseBeneficiary, String, EnterpriseBeneficiaryDao> {
    
    /**
     * 根据统一社会信用代码查询最终受益人信息
     * @param creditCode 统一社会信用代码
     * @return 最终受益人信息列表
     */
    public List<EnterpriseBeneficiary> findByCreditCode(String creditCode) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.findByCreditCode(creditCode, tenantId);
    }
    
    /**
     * 批量保存最终受益人信息
     * @param creditCode 统一社会信用代码
     * @param beneficiaryList 受益人信息列表
     */
    public void batchSave(String creditCode, List<EnterpriseBeneficiary> beneficiaryList) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        Date now = new Date();

        // 检查是否有现有数据
        List<EnterpriseBeneficiary> existingList = dao.findByCreditCode(creditCode, tenantId);
        boolean isUpdate = existingList != null && !existingList.isEmpty();

        // 先删除现有数据
        if (isUpdate) {
            dao.deleteByCreditCode(creditCode, tenantId);
        }

        // 批量保存新数据
        if (beneficiaryList != null && !beneficiaryList.isEmpty()) {
            for (EnterpriseBeneficiary beneficiary : beneficiaryList) {
                beneficiary.setCreditCode(creditCode);
                beneficiary.setTenantId(tenantId);

                if (isUpdate) {
                    // 如果是更新，保持原有的创建时间逻辑，但设置更新时间
                    beneficiary.setCreateTime(now); // 由于删除重建，这里还是设置为当前时间
                    beneficiary.setUpdateTime(now);
                } else {
                    // 新建记录
                    beneficiary.setCreateTime(now);
                    beneficiary.setUpdateTime(now);
                }

                dao.save(beneficiary);
            }
        }
    }
}
