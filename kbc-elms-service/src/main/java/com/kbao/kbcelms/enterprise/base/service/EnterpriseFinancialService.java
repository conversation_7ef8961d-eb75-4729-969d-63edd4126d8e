package com.kbao.kbcelms.enterprise.base.service;

import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.enterprise.base.dao.EnterpriseFinancialDao;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseFinancial;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 上市公司财务简析业务逻辑层
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class EnterpriseFinancialService extends BaseMongoServiceImpl<EnterpriseFinancial, String, EnterpriseFinancialDao> {
    
    /**
     * 根据统一社会信用代码查询财务信息
     * @param creditCode 统一社会信用代码
     * @return 财务信息列表
     */
    public List<EnterpriseFinancial> findByCreditCode(String creditCode) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.findByCreditCode(creditCode, tenantId);
    }
    

    
    /**
     * 批量保存财务信息
     * @param creditCode 统一社会信用代码
     * @param financialList 财务信息列表
     */
    public void batchSave(String creditCode, List<EnterpriseFinancial> financialList) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        Date now = new Date();

        // 检查是否有现有数据
        List<EnterpriseFinancial> existingList = dao.findByCreditCode(creditCode, tenantId);
        boolean isUpdate = existingList != null && !existingList.isEmpty();

        // 先删除现有数据
        if (isUpdate) {
            dao.deleteByCreditCode(creditCode, tenantId);
        }

        // 批量保存新数据
        if (financialList != null && !financialList.isEmpty()) {
            for (EnterpriseFinancial financial : financialList) {
                financial.setCreditCode(creditCode);
                financial.setTenantId(tenantId);

                if (isUpdate) {
                    // 如果是更新，保持原有的创建时间逻辑，但设置更新时间
                    financial.setCreateTime(now); // 由于删除重建，这里还是设置为当前时间
                    financial.setUpdateTime(now);
                } else {
                    // 新建记录
                    financial.setCreateTime(now);
                    financial.setUpdateTime(now);
                }

                dao.save(financial);
            }
        }
    }
    
    /**
     * 保存或更新单条财务信息
     * @param financial 财务信息
     * @return 保存后的财务信息
     */
    public EnterpriseFinancial saveOrUpdate(EnterpriseFinancial financial) {
        Date now = new Date();

        // 设置租户ID
        if (financial.getTenantId() == null) {
            financial.setTenantId(SysLoginUtils.getUser().getTenantId());
        }

        // 检查是否已存在相同企业的财务数据
        String tenantId = SysLoginUtils.getUser().getTenantId();
        List<EnterpriseFinancial> existingList = dao.findByCreditCode(financial.getCreditCode(), tenantId);
        if (existingList != null && !existingList.isEmpty()) {
            // 更新现有记录（通常一个企业只有一条财务汇总记录）
            EnterpriseFinancial existing = existingList.get(0);
            financial.setId(existing.getId());
            financial.setCreateTime(existing.getCreateTime());
            financial.setUpdateTime(now); // 设置更新时间
        } else {
            // 新建记录
            financial.setCreateTime(now);
            financial.setUpdateTime(now);
        }

        return dao.saveOrUpdate(financial);
    }
}
