package com.kbao.kbcelms.processdefine.service;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.bpm.ElmsBpmWebService;
import com.kbao.kbcbpm.process.vo.ProcessDefinitionJsonVO;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.constants.ElmsConstants;
import com.kbao.kbcelms.enums.ProcessDefineStatusEnum;
import com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper;
import com.kbao.kbcelms.processdefine.entity.ProcessDefine;
import com.kbao.kbcelms.processdefine.model.ProcessDefineCotent;
import com.kbao.kbcelms.processdefine.vo.ProcessDefineQueryVO;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ProcessDefineService extends BaseSQLServiceImpl<ProcessDefine, Integer,ProcessDefineMapper> {

    @Autowired
    ProcessDefineCotentService processDefineCotentService;

    @Autowired
    ElmsBpmWebService elmsBpmWebService;

    /**
     * 启动流程
     * @param param
     */
    public void processStart(ProcessDefine param){
        if (param.getId() == null){
            throw new BusinessException("id 不能为空");
        }
        ProcessDefine processDefine = selectByPrimaryKey(param.getId());
        if (processDefine == null){
            throw new BusinessException("流程不存在");
        }
        processDefine.setProcessStatus(ProcessDefineStatusEnum.START.getStatus());
        processDefine.setUpdateId(BscUserUtils.getUserId());
        processDefine.setUpdateTime(new Date());
        this.updateByPrimaryKey(processDefine);

        /**
         * @!todo 部署流程定义
         */
    }

    /**
     * 启动流程
     * @param param
     */
    public void processStop(ProcessDefine param){
        if (param.getId() == null){
            throw new BusinessException("id 不能为空");
        }
        ProcessDefine processDefine = selectByPrimaryKey(param.getId());
        if (processDefine == null){
            throw new BusinessException("流程不存在");
        }
        processDefine.setProcessStatus(ProcessDefineStatusEnum.STOP.getStatus());
        processDefine.setUpdateId(BscUserUtils.getUserId());
        processDefine.setUpdateTime(new Date());
        this.updateByPrimaryKey(processDefine);
        /**
         * @todo 停止所有进行中的流程
         */
    }

    /**
     * 删除流程
     */
    public void processDelete(ProcessDefine param){
        if (param.getId() == null){
            throw new BusinessException("id 不能为空");
        }
        ProcessDefine processDefine = selectByPrimaryKey(param.getId());
        if (processDefine == null){
            throw new BusinessException("流程不存在");
        }
        this.delete(param.getId());
    }

    /**
     * 复制流程
     */
    public ProcessDefine processCopy(ProcessDefine param){
        if (param.getId() == null){
            throw new BusinessException("id 不能为空");
        }
        ProcessDefine processDefine = selectByPrimaryKey(param.getId());
        if (processDefine == null){
            throw new BusinessException("流程不存在");
        }
        processDefine.setId(null);
        processDefine.setProcessName(processDefine.getProcessName() + "副本");
        processDefine.setCreateId(BscUserUtils.getUserId());
        processDefine.setCreateTime(new Date());
        processDefine.setUpdateId(BscUserUtils.getUserId());
        processDefine.setUpdateTime(new Date());
        processDefine.setProcessStatus(ProcessDefineStatusEnum.TEMP.getStatus());
        this.insert(processDefine);
        processDefine.setProcessKey(ElmsBpmWebService.getBpmId(processDefine.getId()));
        this.updateByPrimaryKey(processDefine);
        //复制流程定义图
        ProcessDefineCotent oldDefine = processDefineCotentService.findById(param.getId());
        oldDefine.setId(processDefine.getId());
        processDefineCotentService.save(oldDefine);
        //部署流程定义
        deployBpm( ElmsConstants.BUSINESS_KEY,processDefine.getProcessKey(),processDefine.getProcessName(),oldDefine.getDefineJson());
        return processDefine;
    }

    /**
     * 保存流程
     */
    public void processSave(ProcessDefine processDefine){
        if (processDefine.getCreateId()==null){
            processDefine.setCreateId(BscUserUtils.getUserId());
            processDefine.setCreateTime(new Date());
        }
        processDefine.setTenantId(SysLoginUtils.getUser().getTenantId());
        processDefine.setIsDeleted(0);
        processDefine.setUpdateId(BscUserUtils.getUserId());
        processDefine.setUpdateTime(new Date());
        processDefine.setProcessStatus(ProcessDefineStatusEnum.TEMP.getStatus());
        if (processDefine.getId() == null){
            int count = this.insert(processDefine);
            if (count <= 0){
                throw new BusinessException("保存流程定义失败");
            }
        }else{
            this.updateByPrimaryKeySelective(processDefine);
        }
        //保存流程key
        processDefine.setProcessKey(ElmsBpmWebService.getBpmId(processDefine.getId()));
        this.updateByPrimaryKey(processDefine);
        //复制流程定义图
        ProcessDefineCotent jsonDefine = new ProcessDefineCotent();
        jsonDefine.setId(processDefine.getId());
        jsonDefine.setDefineJson(JSONObject.toJSONString(processDefine.getBpmJson()));
        processDefineCotentService.saveOrUpdate(jsonDefine);

        //部署流程定义
        deployBpm( ElmsConstants.BUSINESS_KEY,processDefine.getProcessKey(),processDefine.getProcessName(),jsonDefine.getDefineJson());
    }

    /**
     * 往bpm部署流程定义
     * @param processKey
     * @param processName
     * @param jsonDefine
     */
    public void deployBpm(String businessKey,String processKey,String processName,String jsonDefine){
        //部署流程定义
        JSONObject json = JSONObject.parseObject(jsonDefine);
        ProcessDefinitionJsonVO jsonVo = json.toJavaObject(ProcessDefinitionJsonVO.class);
        jsonVo.setProcessKey(processKey);
        jsonVo.setProcessName(processName);
        jsonVo.setBusinessKey(businessKey);
        Result res = elmsBpmWebService.deployProcessByJsonWithModel(jsonVo);
        if (!ResultStatusEnum.isSuccess(res.getResp_code())){
            throw new BusinessException(res.getResp_code(),res.getResp_msg());
        }
    }

    /**
     * 查询启用中的流程定义列表
     * 只查询isDeleted=0且流程状态为启用（ProcessDefineStatusEnum.ENABLE.getStatus()）
     * @return 启用中的流程定义列表
     */
    public List<ProcessDefine> getStartProcessDefineList() {
        Map<String, Object> param = new HashMap<>();
        param.put("processStatus",ProcessDefineStatusEnum.START.getStatus());
        return this.selectByParam(param);
    }

    /**
     * 分页查询流程定义列表
     * @param pageRequest
     * @return
     */
    public PageInfo<ProcessDefine> pageWithQuery(RequestObjectPage<ProcessDefineQueryVO> pageRequest) {
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        Map<String, Object> param = new HashMap<>();
        
        ProcessDefineQueryVO queryVO = pageRequest.getParam();
        if (queryVO != null) {
            if (EmptyUtils.isNotEmpty(queryVO.getProcessType())) {
                param.put("processType", queryVO.getProcessType());
            }
            if (EmptyUtils.isNotEmpty(queryVO.getProcessName())) {
                param.put("processName", queryVO.getProcessName());
            }
            if (EmptyUtils.isNotEmpty(queryVO.getProcessStatus())) {
                param.put("processStatus", queryVO.getProcessStatus());
            }
            if (EmptyUtils.isNotEmpty(queryVO.getProcessCondition())) {
                param.put("processCondition", queryVO.getProcessCondition());
            }
            if (EmptyUtils.isNotEmpty(queryVO.getTenantId())) {
                param.put("tenantId", queryVO.getTenantId());
            }
            if (EmptyUtils.isNotEmpty(queryVO.getCreateTimeStart())) {
                param.put("createTimeStart", queryVO.getCreateTimeStart());
            }
            if (EmptyUtils.isNotEmpty(queryVO.getCreateTimeEnd())) {
                param.put("createTimeEnd", queryVO.getCreateTimeEnd());
            }
            if (EmptyUtils.isNotEmpty(queryVO.getCreateName())) {
                param.put("createName", queryVO.getCreateName());
            }
        }
        
        Page<ProcessDefine> page = (Page<ProcessDefine>) this.selectByParam(param);
        return new PageInfo<>(page);
    }

    /**
     * 根据ID查询流程定义详情
     * @param id 流程定义ID
     * @return 流程定义详情
     */
    public ProcessDefine getProcessDefineDetail(Integer id) {
        if (EmptyUtils.isEmpty(id)) {
            throw new BusinessException("流程定义ID不能为空");
        }
        
        ProcessDefine processDefine = this.selectByPrimaryKey(id);
        if (processDefine == null) {
            throw new BusinessException("流程定义不存在");
        }
        
        // 查询流程定义内容（BPM JSON）
        try {
            ProcessDefineCotent content = processDefineCotentService.findById(id);
            if (content != null && EmptyUtils.isNotEmpty(content.getDefineJson())) {
                // 将JSON字符串转换为BpmJson对象
                JSONObject jsonObject = JSONObject.parseObject(content.getDefineJson());
                ProcessDefine.BpmJson bpmJson = jsonObject.toJavaObject(ProcessDefine.BpmJson.class);
                processDefine.setBpmJson(bpmJson);
            }
        } catch (Exception e) {
            // log.warn("解析流程定义JSON失败，ID: {}", id, e); // Original code had this line commented out
        }
        
        return processDefine;
    }
}
