package com.kbao.kbcelms.genAgentEnterprise.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 顾问企业表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Service
public class GenAgentEnterpriseService extends BaseSQLServiceImpl<GenAgentEnterprise, Integer, GenAgentEnterpriseMapper> {

    /**
     * 分页查询顾问企业列表
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    public PageInfo<AgentEnterpriseListResVo> getAgentEnterpriseList(PageRequest<AgentEnterpriseListReqVo> pageRequest) {
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<AgentEnterpriseListResVo> list = mapper.getAgentEnterpriseList(pageRequest.getParam());
        return new PageInfo<>(list);
    }
}
