package com.kbao.kbcelms.opportunityfiles.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.enums.FileTypeEnum;
import com.kbao.kbcelms.opportunityfiles.dao.OpportunityFilesDao;
import com.kbao.kbcelms.opportunityfiles.model.OpportunityFiles;
import com.kbao.kbcelms.ufs.FileWebService;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.IDUtils;
import com.kbao.tool.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


import java.util.Date;
import java.util.List;

/**
 * 机会文件资料表
 */
@Service
public class OpportunityFilesService extends BaseMongoServiceImpl<OpportunityFiles, String, OpportunityFilesDao> {

    @Autowired
    FileWebService fileWebService;

    /**
     * 根据机会id获取文件资料
     *
     * @param opportunityId
     * @param tenantId
     * @return
     */
    public List<OpportunityFiles> findByOpportunityId(Integer opportunityId, String tenantId) {
        if (opportunityId == null) {
            throw new BusinessException("机会id不能为空");
        }

        return this.dao.findByOpportunityId(opportunityId, tenantId);
    }

    /**
     * 删除机会文件资料
     *
     * @param fileId
     */
    public void remove(String fileId) {
        if (StringUtil.isEmpty(fileId)) {
            throw new BusinessException("资料id不能为空");
        }

        this.dao.remove(fileId);
    }


    /**
     * 上传文件
     *
     * @param appCode
     * @param file
     * @param opportunityId
     * @param fileType
     * @param companyId
     * @param companyName
     */
    public void upload(String appCode, MultipartFile file, Integer opportunityId,String fileType,String companyId,String companyName) {
        // 上传文件
        String businessNo = IDUtils.randomUUID();
        FileUploadResponse response = fileWebService.upload(appCode,businessNo,file,null,file.getOriginalFilename(), FileTypeEnum.getByKey(fileType));
        if(response != null) {
            throw new BusinessException("上传文件结果为空");
        }
        OpportunityFiles newFile = new OpportunityFiles();
        newFile.setOpportunityId(opportunityId);
        newFile.setFileType(fileType);
        newFile.setFileName(file.getOriginalFilename());
        newFile.setFilePath(response.getForeignPath());
        newFile.setCompanyId(companyId);
        newFile.setCompanyName(companyName);
        newFile.setCreateTime(new Date());
        this.dao.save(newFile);
    }
}