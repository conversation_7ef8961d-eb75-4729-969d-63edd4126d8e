package com.kbao.kbcelms.bascode.service;


import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.bascode.dao.BasCodeMapper;
import com.kbao.kbcelms.bascode.entity.BasCode;
import com.kbao.kbcelms.bascode.vo.BaseCodeTreeVO;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
public class BasCodeService extends BaseSQLServiceImpl<BasCode, Integer,BasCodeMapper> {

    /**
     * 查询所有BaseCode并组装为树形结构
     */
    public List<BaseCodeTreeVO> getBaseCodeTree() {
        List<BasCode> all = this.selectByParam(null);
        Map<String, BaseCodeTreeVO> codeMap = new HashMap<>();
        List<BaseCodeTreeVO> roots = new ArrayList<>();
        for (BasCode code : all) {
            BaseCodeTreeVO node = new BaseCodeTreeVO(code.getCode(), code.getName());
            codeMap.put(code.getCode(), node);
        }
        for (BasCode code : all) {
            String parentCode = code.getParentCode();
            if (parentCode == null || parentCode.isEmpty() || !codeMap.containsKey(parentCode)) {
                roots.add(codeMap.get(code.getCode()));
            } else {
                codeMap.get(parentCode).getChildren().add(codeMap.get(code.getCode()));
            }
        }
        return roots;
    }

    /**
     * 判断地址code1是否属于地址code2（包括相等或子集关系）
     * @param code1 地址code1
     * @param code2 地址code2
     * @return true: code1属于code2, false: 不属于
     */
    public boolean isAddressCodeBelongTo(String code1, String code2) {
        if (code1 == null || code2 == null) {
            return false;
        }
        
        // 如果两个code相等，直接返回true
        if (code1.equals(code2)) {
            return true;
        }
        
        // 查询code1的所有父级code，判断是否包含code2
        return isCodeInParentHierarchy(code1, code2);
    }
    
    /**
     * 递归判断code是否在指定父级code的层级中
     * @param code 当前code
     * @param targetParentCode 目标父级code
     * @return true: 在层级中, false: 不在
     */
    private boolean isCodeInParentHierarchy(String code, String targetParentCode) {
        // 查询当前code的父级code
        BasCode currentCode = this.selectByParam(new HashMap<String, Object>() {{
            put("code", code);
        }}).stream().findFirst().orElse(null);
        
        if (currentCode == null || currentCode.getParentCode() == null) {
            return false;
        }
        
        // 如果父级code等于目标code，返回true
        if (targetParentCode.equals(currentCode.getParentCode())) {
            return true;
        }
        
        // 递归查询父级的父级
        return isCodeInParentHierarchy(currentCode.getParentCode(), targetParentCode);
    }
}
