package com.kbao.kbcelms.dataTemplate.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.kbcelms.common.TemplateContents;
import com.kbao.kbcelms.common.enums.FieldTypeEnum;
import com.kbao.kbcelms.dataTemplate.bean.DataReqVo;
import com.kbao.kbcelms.dataTemplate.entity.DataTemplate;
import com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper;
import com.kbao.tool.util.SysLoginUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kbao.kbcelms.dataTemplate.model.DataTemplateField;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;import java.util.stream.Collectors;

@Service
public class DataTemplateService extends BaseSQLServiceImpl<DataTemplate, Integer, DataTemplateMapper> {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DataTemplateFieldService dataTemplateFieldService;

    /**
     * 新增数据模板及其字段
     */
    @Transactional(rollbackFor = Exception.class)
    public void addWithFields(DataTemplate dataTemplate) {
        // 校验bizCode唯一性
        if (dataTemplate.getBizCode() == null) {
            throw new RuntimeException("模板编号不能为空");
        }
        int count = mapper.isExistTemplateCode(dataTemplate.getBizCode(), null);
        if (count > 0) {
            throw new RuntimeException("模板编号已存在");
        }
        String tableName = TemplateContents.getTemplateTableName(dataTemplate.getBizCode());
        boolean existTable = this.isExistTable(tableName);
        if (existTable) {
            throw new RuntimeException("该模板编号已存在数据表");
        }
        List<DataTemplateField> fields = dataTemplate.getFields();
        List<String> indexFields = this.getIndexFieldsAndCheck(fields);
        dataTemplate.setTenantId(BscUserUtils.getUser().getUser().getTenantId());
        dataTemplate.setCreateId(SysLoginUtils.getUserId());
        this.insert(dataTemplate);
        dataTemplateFieldService.saveBatch(dataTemplate.getId(), fields);
        //创建数据表
        mapper.ddlCreateTable(tableName, fields, indexFields, dataTemplate.getTemplateName());
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateWithFields(DataTemplate dataTemplate) {
        if (dataTemplate.getBizCode() == null) {
            throw new RuntimeException("模板编号不能为空");
        }
        int count = mapper.isExistTemplateCode(dataTemplate.getBizCode(), dataTemplate.getId());
        if (count > 0) {
            throw new RuntimeException("模板编号已存在");
        }
        dataTemplate.setUpdateId(SysLoginUtils.getUserId());
        String tableName = TemplateContents.getTemplateTableName(dataTemplate.getBizCode());
        boolean isExist = this.isExistData(tableName);
        if (!isExist) {
            // 没有数据，直接删除表重建，不校验变更
            List<DataTemplateField> fields = dataTemplate.getFields();
            List<String> indexFields = this.getIndexFieldsAndCheck(fields);
            mapper.ddlDropTable(tableName);
            mapper.updateByPrimaryKeySelective(dataTemplate);
            // 保存所有字段
            dataTemplateFieldService.saveBatch(dataTemplate.getId(), dataTemplate.getFields());
            mapper.ddlCreateTable(tableName, fields, indexFields, dataTemplate.getTemplateName());
            return;
        }
        // 存在数据，不允许修改字段类型和长度
        updateWithData(tableName, dataTemplate);
    }

    private void updateWithData(String tableName, DataTemplate dataTemplate) {
        List<DataTemplateField> newFields = dataTemplate.getFields();
        List<DataTemplateField> oldFields = dataTemplateFieldService.getFieldList(dataTemplate.getId());
        Map<String, DataTemplateField> oldFieldMap = oldFields.stream()
                .collect(Collectors.toMap(DataTemplateField::getFieldCode, f -> f));
        Map<String, DataTemplateField> newFieldMap = newFields.stream()
                .collect(Collectors.toMap(DataTemplateField::getFieldCode, f -> f, (a, b) -> a));
        for (String oldCode : oldFieldMap.keySet()) {
            DataTemplateField oldField = oldFieldMap.get(oldCode);
            DataTemplateField newField = newFieldMap.get(oldCode);
            if (newField == null || !oldField.getFieldType().equals(newField.getFieldType())
                    || !Objects.equals(oldField.getFieldLength(), newField.getFieldLength())) {
                throw new RuntimeException("不允许修改或删除字段编码/类型/长度：" + oldCode);
            }
        }
        // 删除索引的字段
        List<String> delIndexFields = oldFields.stream().filter(f ->
                "1".equals(f.getIsIndex()) && !newFieldMap.get(f.getFieldCode()).getIsIndex().equals("1")
        ).map(DataTemplateField::getFieldCode).collect(Collectors.toList());

        List<DataTemplateField> addFields = newFields.stream().filter(f ->
                !oldFieldMap.containsKey(f.getFieldCode())
        ).collect(Collectors.toList());
        List<String> indexFields = this.getIndexFieldsAndCheck(addFields);
        this.updateByPrimaryKeySelective(dataTemplate);
        // 保存所有字段
        dataTemplateFieldService.saveBatch(dataTemplate.getId(), newFields);
        if (!addFields.isEmpty() || !indexFields.isEmpty() || CollectionUtils.isNotEmpty(delIndexFields)) {
            mapper.ddlChangeTable(tableName, addFields, indexFields, delIndexFields);
        }
    }

    private List<String> getIndexFieldsAndCheck(List<DataTemplateField> fields) {
        List<String> indexFields = new ArrayList<>();
        fields.forEach(item -> {
            String fieldType = FieldTypeEnum.buildFieldType(item.getFieldType(), item.getFieldLength());
            item.setFieldTypeStr(fieldType);
            if ("1".equals(item.getIsIndex())) {
                indexFields.add(item.getFieldCode());
            }
        });
        return indexFields;
    }

    public DataTemplate getById(Integer id) {
        DataTemplate dataTemplate = mapper.selectByPrimaryKey(id);
        List<DataTemplateField> fieldList = dataTemplateFieldService.getFieldList(dataTemplate.getId());
        dataTemplate.setFields(fieldList);
        return dataTemplate;
    }

    public List<DataTemplateField> getFieldList(DataReqVo reqVo) {
        DataTemplate dataTemplate = mapper.selectByBizCode(reqVo.getTemplateCode());
        return dataTemplateFieldService.getFieldList(dataTemplate.getId());
    }

    public PageInfo<Map> getDataList(PageRequest<DataReqVo> reqVo) {
        DataReqVo param = reqVo.getParam();
        String tableName = TemplateContents.getTemplateTableName(param.getTemplateCode());

        if (!isExistTable(tableName)) {
            return new PageInfo<>();
        }
        param.setTableName(tableName);
        PageHelper.startPage(reqVo.getPageNum(), reqVo.getPageSize());
        List<Map> list = mapper.getDataList(param);
        return new PageInfo<>(list);
    }

    public boolean isExistData(String tableName) {
        int existTable = mapper.isExistTable(tableName);
        if (existTable == 0) {
            return false;
        }
        return mapper.isExistData(tableName) > 0;
    }

    public boolean isExistTable(String tableName) {
        return mapper.isExistTable(tableName) > 0;
    }
}