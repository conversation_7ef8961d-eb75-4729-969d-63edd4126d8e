package com.kbao.kbcelms.user.dao;

import java.util.*;
import java.util.Map;

import com.kbao.kbcelms.user.vo.BranchCoordinatorResponseVO;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.user.entity.User;
import com.kbao.kbcelms.user.vo.UserResponseVO;
import com.kbao.kbcelms.user.vo.UserRoleAuthVO;
import org.apache.ibatis.annotations.Param;

public interface UserMapper extends BaseMapper<User, Integer> {

    User getByBscUserNameAndTenantId(@Param("tenantId") String tenantId, @Param("bscUseName") String bscUseName);

    User findByUserId(@Param("userId") String userId);

    User findByBscUserName(@Param("bscUseName") String bscUseName);

    UserRoleAuthVO getUserRoleAuthByUserId(@Param("userId") String userId, @Param("tenantId") String tenantId);

    List<UserResponseVO> findByCondition(Map<String, Object> params);

    UserResponseVO detail(Map<String, Object> params);

    /**
     * 查询分公司统筹角色人员清单
     * @param roleType 角色性质（必填）
     * @param organCode 机构编码（可为空，为空时查询所有机构）
     * @param nickName 用户姓名（可为空，为空时查询所有用户，不为空时进行模糊搜索）
     * @return 人员清单
     * <AUTHOR>
     * @date 2025/1/15 16:30
     */
    List<Map<String, Object>> selectBranchCoordinatorUsers(@Param("roleType") int roleType, @Param("organCode") String organCode, @Param("nickName") String nickName);
}