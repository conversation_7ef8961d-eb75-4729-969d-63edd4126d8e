package com.kbao.kbcelms.opportunityprocesslog.service;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunityprocesslog.entity.OpportunityProcessLog;
import com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseReasonQueryResponseVO;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.kbcelms.enums.OpportunityCloseReasonEnum;


@Service
public class OpportunityProcessLogService extends BaseSQLServiceImpl<OpportunityProcessLog, Integer,OpportunityProcessLogMapper> {

	/**
	 * 查询用户参与过的机会数量
	 * @param userId 用户ID
	 * @param tenantId 租户ID
	 * @return 参与过的机会数量
	 */
	public int countUserParticipatedOpportunities(String userId, String tenantId) {
		if (EmptyUtils.isEmpty(userId) || EmptyUtils.isEmpty(tenantId)) {
			return 0;
		}
		return mapper.countUserParticipatedOpportunities(userId, tenantId);
	}
	
	/**
	 * 根据机会ID查询关闭原因
	 * @param opportunityId 机会ID
	 * @param tenantId 租户ID
	 * @return 关闭原因信息
	 */
	public OpportunityCloseReasonQueryResponseVO getCloseReasonByOpportunityId(Integer opportunityId, String tenantId) {
		if (opportunityId == null || EmptyUtils.isEmpty(tenantId)) {
			return null;
		}
		OpportunityCloseReasonQueryResponseVO result = mapper.getCloseReasonByOpportunityId(opportunityId, tenantId);
		if (result != null && result.getReasonType() != null) {
			// 设置关闭原因类型名称
			result.setReasonTypeName(OpportunityCloseReasonEnum.getNameByCode(result.getReasonType()));
			
			// 生成格式化后的操作描述
			String formattedDesc = generateFormattedOperaorDesc(result);
			result.setFormattedOperaorDesc(formattedDesc);
		}
		return result;
	}
	
	/**
	 * 生成格式化后的操作描述
	 * @param result 查询结果
	 * @return 格式化后的描述
	 */
	private String generateFormattedOperaorDesc(OpportunityCloseReasonQueryResponseVO result) {
		StringBuilder sb = new StringBuilder();
		
		// 添加机构信息
		if (!EmptyUtils.isEmpty(result.getOperatorOrgPath())) {
			sb.append(result.getOperatorOrgPath());
		}
		
		// 添加角色名称
//		if (!EmptyUtils.isEmpty(result.getOperatorRoleName())) {
//			if (sb.length() > 0) {
//				sb.append(" ");
//			}
//			sb.append(result.getOperatorRoleName());
//		}
		
		// 添加用户姓名
		if (!EmptyUtils.isEmpty(result.getOperatorName())) {
			if (sb.length() > 0) {
				sb.append(" ");
			}
			sb.append(result.getOperatorName());
		}
		
		// 添加用户名（括号形式）
		if (!EmptyUtils.isEmpty(result.getOperatorUserName())) {
			sb.append("（").append(result.getOperatorUserName()).append("）");
		}
		
		// 添加操作动作
		sb.append("变更为\"已关闭-").append(result.getReasonTypeName()).append("\"");
		
		return sb.toString();
	}

	/**
	 * 构建操作描述文案
	 * @param operatorRole 操作人角色
	 * @param operatorName 操作人姓名
	 * @param operatorUserName 操作人用户名
	 * @param action 操作动作
	 * @param actionInfo 操作信息（可选）
	 * @param assigneeOrg 指派机构（可选）
	 * @param assigneeRole 指派角色（可选）
	 * @param assigneeName 指派姓名（可选）
	 * @param assigneeUserName 指派用户名（可选）
	 * @return 格式化后的操作描述
	 */
	public String buildOperaorDesc(String operatorRole, String operatorName, String operatorUserName, String action, String actionInfo,
								   String assigneeOrg, String assigneeRole, String assigneeName, String assigneeUserName) {
		StringBuilder sb = new StringBuilder();
		sb.append("<div>");
		// 操作人信息
		if (!EmptyUtils.isEmpty(operatorRole)) {
			sb.append(operatorRole);
		}
		if (!EmptyUtils.isEmpty(operatorName)) {
			if (sb.length() > 0) sb.append(" ");
			sb.append("<span class=\"info-name\">").append(operatorName);
			if (!EmptyUtils.isEmpty(operatorUserName)) {
				sb.append("（").append(operatorUserName).append("）");
			}
			sb.append("</span>");
		}

		// 操作动作
		if (!EmptyUtils.isEmpty(action)) {
			if (sb.length() > 0) sb.append(" ");
			sb.append(action);
		}

		if (!EmptyUtils.isEmpty(actionInfo)) {
			if (sb.length() > 0) sb.append(" ");
			sb.append("<span class=\"info-task\">").append(actionInfo).append("</span>");
		}
		
		// 指派信息
		if (!EmptyUtils.isEmpty(assigneeOrg) || !EmptyUtils.isEmpty(assigneeRole) || !EmptyUtils.isEmpty(assigneeName)) {
			if (sb.length() > 0) sb.append(" ");
			if (!EmptyUtils.isEmpty(assigneeOrg)) {
				sb.append(assigneeOrg);
			}
			if (!EmptyUtils.isEmpty(assigneeRole)) {
				if (sb.length() > 0 && !sb.toString().endsWith(assigneeOrg)) sb.append(" ");
				sb.append(assigneeRole);
			}
			if (!EmptyUtils.isEmpty(assigneeName)) {
				if (sb.length() > 0) sb.append(" ");
				sb.append(assigneeName);
			}
			if (!EmptyUtils.isEmpty(assigneeUserName)) {
				sb.append("（").append(assigneeUserName).append("）");
			}
		}
		sb.append("</div>");
		return sb.toString();
	}
}
