package com.kbao.kbcelms.auth.service;


import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Maps;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.auth.vo.AuthAddVO;
import com.kbao.kbcelms.auth.vo.AuthRequestVO;
import com.kbao.kbcelms.auth.vo.AuthTreeVO;
import com.kbao.kbcums.appsms.enums.IsDeleteEnum;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.auth.entity.Auth;
import com.kbao.kbcelms.auth.dao.AuthMapper;


@Service
public class AuthService extends BaseSQLServiceImpl<Auth, Integer, AuthMapper> {

    public List<Auth> findAll() {
        return mapper.selectAll(Maps.newHashMap());
    }

    /**
     * 权限增改
     *
     * @param authAddVO
     * <AUTHOR>
     * @Date 2025/7/22 15:41
     */
    public void saveOrUpdate(AuthAddVO authAddVO) {
        if (EmptyUtils.isEmpty(authAddVO.getId())) {
            this.checkRepeat(authAddVO.getAuthCode());
            Auth auth = new Auth();
            auth.setAuthCode(authAddVO.getAuthCode());
            auth.setAuthName(authAddVO.getAuthName());
            auth.setParentCode(authAddVO.getParentCode());
            auth.setCreateId(SysLoginUtils.getUserId());
            auth.setCreateTime(DateUtils.getCurrentDate());
            auth.setIsDeleted(IsDeleteEnum.NO.getCode());
            auth.setSort(this.getSort(authAddVO.getParentCode()));
            mapper.insert(auth);
        } else {
            Auth auth = new Auth();
            auth.setId(authAddVO.getId());
            auth.setAuthName(authAddVO.getAuthName());
            auth.setUpdateId(SysLoginUtils.getUserId());
            auth.setUpdateTime(DateUtils.getCurrentDate());
            mapper.updateByPrimaryKeySelective(auth);
        }
    }

    /**
     * 权限编码重复校验
     *
     * @param authCode
     * <AUTHOR>
     * @Date 2025/7/22 15:41
     */
    public void checkRepeat(String authCode) {
        List<Auth> list = mapper.findByAuthCode(authCode);
        if (EmptyUtils.isNotEmpty(list)) {
            throw new BusinessException("该权限位编码已存在");
        }
    }

    /**
     * 获取排序序号
     *
     * @param parentCode
     * <AUTHOR>
     * @Date 2025/7/22 17:51
     */
    public int getSort(String parentCode) {
        if (EmptyUtils.isEmpty(parentCode)) {
            List<Auth> list = mapper.findParentAuth();
            return EmptyUtils.isEmpty(list) ? 1 : list.size() + 1;
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("parentCode", parentCode);
        List<Auth> list = mapper.selectAll(map);
        return EmptyUtils.isEmpty(list) ? 1 : list.size() + 1;
    }

}
