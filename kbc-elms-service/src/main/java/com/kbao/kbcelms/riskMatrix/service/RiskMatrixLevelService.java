package com.kbao.kbcelms.riskMatrix.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskMatrix.entity.RiskMatrixLevel;
import com.kbao.kbcelms.riskMatrix.dao.RiskMatrixLevelMapper;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 风险矩阵档次配置服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class RiskMatrixLevelService extends BaseSQLServiceImpl<RiskMatrixLevel, Long, RiskMatrixLevelMapper> {
    
    /**
     * 根据类别ID查询档次列表
     * 
     * @param categoryId 类别ID
     * @return 档次列表
     */
    public List<RiskMatrixLevel> getByCategoryId(Long categoryId) {
        return mapper.selectByCategoryId(categoryId);
    }
    
    /**
     * 保存档次配置
     *
     * @param categoryId 类别ID
     * @param levels 档次列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLevels(Long categoryId, List<RiskMatrixLevel> levels) {
        if (categoryId == null || CollectionUtils.isEmpty(levels)) {
            return false;
        }

        // 先删除原有档次配置
        mapper.deleteByCategoryId(categoryId);

        // 设置类别ID和时间
        LocalDateTime now = LocalDateTime.now();
        for (RiskMatrixLevel level : levels) {
            level.setCategoryId(categoryId);
            level.setCreateTime(now);
            level.setUpdateTime(now);
        }

        // 批量插入新的档次配置
        return mapper.batchInsert(levels) > 0;
    }

    /**
     * 保存档次配置（从请求参数）
     *
     * @param categoryId 类别ID
     * @param levelRequests 档次请求参数列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLevelsFromRequests(Long categoryId, List<RiskMatrixRequest.LevelRequest> levelRequests) {
        if (categoryId == null || CollectionUtils.isEmpty(levelRequests)) {
            return false;
        }

        // 转换请求参数为实体对象
        List<RiskMatrixLevel> levels = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (int i = 0; i < levelRequests.size(); i++) {
            RiskMatrixRequest.LevelRequest levelRequest = levelRequests.get(i);
            RiskMatrixLevel level = new RiskMatrixLevel();
            BeanUtils.copyProperties(levelRequest, level);

            level.setCategoryId(categoryId);
            level.setMinValue(levelRequest.getMinValue() != null ?
                    BigDecimal.valueOf(levelRequest.getMinValue()) : null);
            level.setMaxValue(levelRequest.getMaxValue() != null ?
                    BigDecimal.valueOf(levelRequest.getMaxValue()) : null);
            level.setSortOrder(i + 1);
            level.setCreateTime(now);
            level.setUpdateTime(now);

            levels.add(level);
        }

        // 调用原有的保存方法
        return saveLevels(categoryId, levels);
    }
    
    /**
     * 根据类别ID删除档次配置
     *
     * @param categoryId 类别ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return;
        }
        mapper.deleteByCategoryId(categoryId);
    }
    
    /**
     * 批量删除档次配置
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return mapper.deleteByIds(ids);
    }
    
    /**
     * 根据类别ID和分值查询对应档次
     * 
     * @param categoryId 类别ID
     * @param score 分值
     * @return 档次
     */
    public RiskMatrixLevel getLevelByScore(Long categoryId, Double score) {
        if (categoryId == null || score == null) {
            return null;
        }
        return mapper.selectByCategoryIdAndScore(categoryId, score);
    }
    
    /**
     * 更新档次配置
     * 
     * @param level 档次配置
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLevel(RiskMatrixLevel level) {
        if (level == null || level.getId() == null) {
            return false;
        }
        
        level.setUpdateTime(LocalDateTime.now());
        return mapper.updateByPrimaryKeySelective(level) > 0;
    }
    
    /**
     * 新增档次配置
     * 
     * @param level 档次配置
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insertLevel(RiskMatrixLevel level) {
        if (level == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        level.setCreateTime(now);
        level.setUpdateTime(now);
        
        return mapper.insertSelective(level) > 0;
    }
}
