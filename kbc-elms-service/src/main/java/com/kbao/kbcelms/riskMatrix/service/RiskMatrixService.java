package com.kbao.kbcelms.riskMatrix.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixQuery;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixRequest;
import com.kbao.kbcelms.riskMatrix.entity.RiskMatrix;
import com.kbao.kbcelms.riskMatrix.entity.RiskMatrixCategory;
import com.kbao.kbcelms.riskMatrix.entity.RiskMatrixLevel;
import com.kbao.kbcelms.riskMatrix.entity.CategoryScoreItem;
import com.kbao.kbcelms.riskMatrix.vo.RiskMatrixVO;
import com.kbao.kbcelms.riskMatrix.vo.ScoreItemVO;
import com.kbao.kbcelms.riskMatrix.dao.RiskMatrixMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 风险矩阵服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class RiskMatrixService extends BaseSQLServiceImpl<RiskMatrix, Long, RiskMatrixMapper> {

    @Autowired
    private RiskMatrixCategoryService categoryService;
    
    @Autowired
    private RiskMatrixLevelService levelService;

    @Autowired
    private CategoryScoreItemService categoryScoreItemService;

    @Autowired
    private ScoreItemService scoreItemService;
    
   
    public PageInfo<RiskMatrixVO> getPage(PageRequest<RiskMatrixQuery> request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<RiskMatrix> list = mapper.selectByQuery(request.getParam());
        PageInfo<RiskMatrix> pageInfo = new PageInfo<>(list);
        
        List<RiskMatrixVO> voList = list.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        PageInfo<RiskMatrixVO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(voList);
        
        return result;
    }
    
   
    public RiskMatrixVO getById(Long id) {
        RiskMatrix entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            return null;
        }
        
        RiskMatrixVO vo = convertToVO(entity);
        
        // 查询类别信息
        List<RiskMatrixCategory> categories = categoryService.getByMatrixId(id);
        if (!CollectionUtils.isEmpty(categories)) {
            List<RiskMatrixVO.CategoryVO> categoryVOs = categories.stream()
                    .map(this::convertCategoryToVO)
                    .collect(Collectors.toList());
            vo.setCategories(categoryVOs);
        }
        
        return vo;
    }
    
   
    @Transactional(rollbackFor = Exception.class)
    public void save(RiskMatrixRequest request, String currentUser) {
        RiskMatrix entity = new RiskMatrix();
        BeanUtils.copyProperties(request, entity);
        
        // 处理企业类型
        if (!CollectionUtils.isEmpty(request.getEnterpriseTypes())) {
            entity.setEnterpriseTypes(String.join(",", request.getEnterpriseTypes()));
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        if (request.getId() != null) {
            // 更新
            entity.setUpdateTime(now);
            entity.setUpdateUser(currentUser);
            mapper.updateByPrimaryKeySelective(entity);
        } else {
            // 新增
            entity.setCode(generateCode(request.getName()));
            entity.setStatus(1);
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setCreateUser(currentUser);
            entity.setUpdateUser(currentUser);
            mapper.insertSelective(entity);
        }
    }
    
   
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        // 查询类别信息，用于删除档次配置
        List<RiskMatrixCategory> categories = categoryService.getByMatrixId(id);
        if (!CollectionUtils.isEmpty(categories)) {
            for (RiskMatrixCategory category : categories) {
                // 删除档次配置
                levelService.deleteByCategoryId(category.getId());
            }
        }

        // 删除类别信息
        categoryService.deleteByMatrixId(id);

        // 删除主记录
        return mapper.deleteByPrimaryKey(id) > 0;
    }
    
   
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        // 删除类别信息和档次配置
        for (Long id : ids) {
            // 查询类别信息，用于删除档次配置
            List<RiskMatrixCategory> categories = categoryService.getByMatrixId(id);
            if (!CollectionUtils.isEmpty(categories)) {
                for (RiskMatrixCategory category : categories) {
                    // 删除档次配置
                    levelService.deleteByCategoryId(category.getId());
                }
            }
            // 删除类别信息
            categoryService.deleteByMatrixId(id);
        }

        // 删除主记录
        return mapper.deleteByIds(ids);
    }
    
   
    public RiskMatrixVO getByCode(String code) {
        RiskMatrix entity = mapper.selectByCode(code);
        return entity != null ? convertToVO(entity) : null;
    }
    
   
    public boolean existsByCode(String code, Long excludeId) {
        RiskMatrix entity = mapper.selectByCode(code);
        if (entity == null) {
            return false;
        }
        return excludeId == null || !excludeId.equals(entity.getId());
    }
    

    
    /**
     * 转换为VO对象
     */
    private RiskMatrixVO convertToVO(RiskMatrix entity) {
        RiskMatrixVO vo = new RiskMatrixVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 处理企业类型
        if (StringUtils.hasText(entity.getEnterpriseTypes())) {
            vo.setEnterpriseTypes(Arrays.asList(entity.getEnterpriseTypes().split(",")));
            vo.setEnterpriseTypesDisplay(entity.getEnterpriseTypes().replace(",", "、"));
        }
        
        // 处理状态名称
        vo.setStatusName(entity.getStatus() == 1 ? "启用" : "禁用");
        
        return vo;
    }
    
    /**
     * 转换类别为VO对象
     */
    private RiskMatrixVO.CategoryVO convertCategoryToVO(RiskMatrixCategory category) {
        RiskMatrixVO.CategoryVO vo = new RiskMatrixVO.CategoryVO();
        BeanUtils.copyProperties(category, vo);
        vo.setWeight(category.getWeight().doubleValue());

        // 处理计算方法名称
        switch (category.getCalculationMethod()) {
            case "sum":
                vo.setCalculationMethodName("求和");
                break;
            case "avg":
                vo.setCalculationMethodName("平均值");
                break;
            case "max":
                vo.setCalculationMethodName("最大值");
                break;
            default:
                vo.setCalculationMethodName(category.getCalculationMethod());
        }

        // 查询档次配置
        List<RiskMatrixLevel> levels = levelService.getByCategoryId(category.getId());
        if (!CollectionUtils.isEmpty(levels)) {
            List<RiskMatrixVO.LevelVO> levelVOs = levels.stream()
                    .map(this::convertLevelToVO)
                    .collect(Collectors.toList());
            vo.setLevels(levelVOs);
        }

        // 查询关联的评分项
        List<CategoryScoreItem> categoryScoreItems = categoryScoreItemService.getByCategoryId(category.getId());
        if (!CollectionUtils.isEmpty(categoryScoreItems)) {
            List<Long> scoreItemIds = categoryScoreItems.stream()
                    .map(CategoryScoreItem::getScoreItemId)
                    .collect(Collectors.toList());
            vo.setScoreItems(scoreItemIds);

            // 查询评分项名称
            List<ScoreItemVO> scoreItems = scoreItemService.getByIds(scoreItemIds);
            if (!CollectionUtils.isEmpty(scoreItems)) {
                List<String> scoreItemNames = scoreItems.stream()
                        .map(ScoreItemVO::getName)
                        .collect(Collectors.toList());
                vo.setScoreItemNames(scoreItemNames);
            }
        }

        return vo;
    }

    /**
     * 转换档次为VO对象
     */
    private RiskMatrixVO.LevelVO convertLevelToVO(RiskMatrixLevel level) {
        RiskMatrixVO.LevelVO vo = new RiskMatrixVO.LevelVO();
        BeanUtils.copyProperties(level, vo);

        // 转换 BigDecimal 为 Double
        if (level.getMinValue() != null) {
            vo.setMinValue(level.getMinValue().doubleValue());
        }
        if (level.getMaxValue() != null) {
            vo.setMaxValue(level.getMaxValue().doubleValue());
        }

        // 设置区间范围显示
        if (level.getMinValue() != null && level.getMaxValue() != null) {
            vo.setRangeDisplay(level.getMinValue() + " - " + level.getMaxValue());
        }

        return vo;
    }

    /**
     * 生成编码
     */
    private String generateCode(String name) {
        // 简单的编码生成逻辑，实际项目中可能需要更复杂的规则
        return name.toUpperCase().replaceAll("[\\s\\u4e00-\\u9fa5]", "_") + "_" + System.currentTimeMillis();
    }
}
