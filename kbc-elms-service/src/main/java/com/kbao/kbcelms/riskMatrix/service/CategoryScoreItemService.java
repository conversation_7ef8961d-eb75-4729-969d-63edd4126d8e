package com.kbao.kbcelms.riskMatrix.service;

import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.riskMatrix.entity.CategoryScoreItem;
import com.kbao.kbcelms.riskMatrix.dao.CategoryScoreItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类别与评分项关联服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
public class CategoryScoreItemService extends BaseSQLServiceImpl<CategoryScoreItem, Long, CategoryScoreItemMapper> {
    
    /**
     * 根据类别ID查询关联的评分项列表
     * 
     * @param categoryId 类别ID
     * @return 关联列表
     */
    public List<CategoryScoreItem> getByCategoryId(Long categoryId) {
        return mapper.selectByCategoryId(categoryId);
    }
    
    /**
     * 根据评分项ID查询关联的类别列表
     * 
     * @param scoreItemId 评分项ID
     * @return 关联列表
     */
    public List<CategoryScoreItem> getByScoreItemId(Long scoreItemId) {
        return mapper.selectByScoreItemId(scoreItemId);
    }
    
    /**
     * 保存类别与评分项的关联关系
     * 
     * @param categoryId 类别ID
     * @param scoreItemIds 评分项ID列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRelations(Long categoryId, List<Long> scoreItemIds) {
        if (categoryId == null || CollectionUtils.isEmpty(scoreItemIds)) {
            return false;
        }
        
        // 先删除原有关联
        mapper.deleteByCategoryId(categoryId);
        
        // 创建新的关联关系
        List<CategoryScoreItem> relations = new java.util.ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (Long scoreItemId : scoreItemIds) {
            CategoryScoreItem relation = new CategoryScoreItem();
            relation.setCategoryId(categoryId);
            relation.setScoreItemId(scoreItemId);
            relation.setCreateTime(now);
            relations.add(relation);
        }
        
        // 批量插入新的关联关系
        return mapper.batchInsert(relations) > 0;
    }
    
    /**
     * 根据类别ID删除关联
     * 
     * @param categoryId 类别ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return false;
        }
        return mapper.deleteByCategoryId(categoryId) >= 0;
    }
    
    /**
     * 根据评分项ID删除关联
     * 
     * @param scoreItemId 评分项ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByScoreItemId(Long scoreItemId) {
        if (scoreItemId == null) {
            return false;
        }
        return mapper.deleteByScoreItemId(scoreItemId) >= 0;
    }
    
    /**
     * 批量删除关联
     * 
     * @param ids ID列表
     * @return 删除数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return mapper.deleteByIds(ids);
    }
    
    /**
     * 检查关联是否存在
     * 
     * @param categoryId 类别ID
     * @param scoreItemId 评分项ID
     * @return 是否存在
     */
    public boolean existsRelation(Long categoryId, Long scoreItemId) {
        if (categoryId == null || scoreItemId == null) {
            return false;
        }
        return mapper.selectByCategoryIdAndScoreItemId(categoryId, scoreItemId) != null;
    }
    
    /**
     * 创建关联
     * 
     * @param categoryId 类别ID
     * @param scoreItemId 评分项ID
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRelation(Long categoryId, Long scoreItemId) {
        if (categoryId == null || scoreItemId == null) {
            return false;
        }
        
        // 检查是否已存在
        if (existsRelation(categoryId, scoreItemId)) {
            return true;
        }
        
        CategoryScoreItem relation = new CategoryScoreItem();
        relation.setCategoryId(categoryId);
        relation.setScoreItemId(scoreItemId);
        relation.setCreateTime(LocalDateTime.now());
        
        return mapper.insertSelective(relation) > 0;
    }
    
    /**
     * 删除关联
     * 
     * @param categoryId 类别ID
     * @param scoreItemId 评分项ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRelation(Long categoryId, Long scoreItemId) {
        if (categoryId == null || scoreItemId == null) {
            return false;
        }
        
        CategoryScoreItem relation = mapper.selectByCategoryIdAndScoreItemId(categoryId, scoreItemId);
        if (relation != null) {
            return mapper.deleteByPrimaryKey(relation.getId()) > 0;
        }
        
        return true;
    }
    
    /**
     * 根据类别ID列表查询关联的评分项
     * 
     * @param categoryIds 类别ID列表
     * @return 关联列表
     */
    public List<CategoryScoreItem> getByCategoryIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new java.util.ArrayList<>();
        }
        return mapper.selectByCategoryIds(categoryIds);
    }
    
    /**
     * 根据评分项ID列表查询关联的类别
     * 
     * @param scoreItemIds 评分项ID列表
     * @return 关联列表
     */
    public List<CategoryScoreItem> getByScoreItemIds(List<Long> scoreItemIds) {
        if (CollectionUtils.isEmpty(scoreItemIds)) {
            return new java.util.ArrayList<>();
        }
        return mapper.selectByScoreItemIds(scoreItemIds);
    }
}
