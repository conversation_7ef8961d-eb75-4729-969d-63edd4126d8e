package com.kbao.kbcelms.opportunityteam.handle;


import com.kbao.kbcelms.common.handler.OperationChangeHandler;
import com.kbao.kbcelms.operationlog.entity.OperationLog;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

@Component("managerChangeHandler")
public class OpportunityManagerChangeHandler implements OperationChangeHandler {

    @Autowired
    private OpportunityTeamService opportunityTeamService;

    @Override
    public Object getBeforeData(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        List<OpportunityTeamMember> teamMembers = (List<OpportunityTeamMember>) args[0];

        List<Integer> opportunityIds = teamMembers.stream()
                .map(OpportunityTeamMember::getOpportunityId)
                .collect(Collectors.toList());

        // 获取变更前的负责人信息
        return opportunityTeamService.getManagers(opportunityIds);
    }

    @Override
    public Object getAfterData(Object[] args, Object result) {
        if (args == null || args.length == 0) {
            return null;
        }

        List<OpportunityTeamMember> teamMembers = (List<OpportunityTeamMember>) args[0];

        List<Integer> opportunityIds = teamMembers.stream()
                .map(OpportunityTeamMember::getOpportunityId)
                .collect(Collectors.toList());

        // 获取变更后的负责人信息
        return opportunityTeamService.getManagers(opportunityIds);
    }

    @Override
    public List<OperationLog> fillLogContent(OperationLog log, Object beforeData, Object afterData) {
        if (beforeData == null || afterData == null) {
            return null;
        }

        List<OpportunityTeamMember> oldManagers = (List<OpportunityTeamMember>) beforeData;
        List<OpportunityTeamMember> newManagers = (List<OpportunityTeamMember>) afterData;

        // 变更后的负责人信息
        Map<Integer, OpportunityTeamMember> newManagersMap = newManagers.stream()
                .collect(Collectors.toMap(
                        OpportunityTeamMember::getOpportunityId,
                        m -> m
                ));

        List<OperationLog> logs = new ArrayList<>();
        for(OpportunityTeamMember member : oldManagers) {

            OperationLog cloneLog = log.clone();
            cloneLog.setOpportunityId(member.getOpportunityId());
            cloneLog.setBusinessId(member.getId().toString());

            OpportunityTeamMember newMember = newManagersMap.get(member.getOpportunityId());

            if(newMember == null) {
                log.setDescription("指定项目经理不存在。");
            }
            else if(member.getUserId().equals(newMember.getUserId())) {
                log.setDescription("项目经理无变更。");
            }
            else {
                Map<String, Object> changes = compareChanges(beforeData, afterData);
                cloneLog.setBeforeChange((Map<String, Object>) changes.get("before"));
                cloneLog.setAfterChange((Map<String, Object>) changes.get("after"));
                cloneLog.setDescription("项目经理从[" + member.getUserName() + "]变更为[" + newMember.getUserName() + "]");

            }

            logs.add(cloneLog);
        }

        return logs;
    }

    @Override
    public Map<String, Object> compareChanges(Object beforeData, Object afterData) {
        Map<String, Object> result = new HashMap<>();

        OpportunityTeamMember oldManager = (OpportunityTeamMember) beforeData;
        OpportunityTeamMember newManager = (OpportunityTeamMember) afterData;

        Map<String, Object> oldManagerMap = new HashMap<>();
        oldManagerMap.put("userId", oldManager.getUserId());
        oldManagerMap.put("userName", oldManager.getUserName());

        Map<String, Object> newManagerMap = new HashMap<>();
        newManagerMap.put("userId", newManager.getUserId());
        newManagerMap.put("userName", newManager.getUserName());

        // 变更前
        result.put("before", oldManagerMap);

        // 变更后
        result.put("after", newManagerMap);

        return result;
    }
}