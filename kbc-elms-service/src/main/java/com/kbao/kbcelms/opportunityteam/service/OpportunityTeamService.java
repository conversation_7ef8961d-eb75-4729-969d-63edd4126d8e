package com.kbao.kbcelms.opportunityteam.service;

import com.alibaba.fastjson.JSON;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcelms.common.annotation.RecordOperation;
import com.kbao.kbcelms.enums.RoleTypeEnum;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService;
import com.kbao.kbcelms.sms.AppSmsWebService;
import com.kbao.kbcelms.sms.MailRequest;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunityteam.entity.OpportunityTeam;
import com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 机会项目成员服务
 */
@Slf4j
@Service
public class OpportunityTeamService extends BaseSQLServiceImpl<OpportunityTeam, Integer,OpportunityTeamMapper> {

    @Autowired
    private AppSmsWebService appSmsWebService;

    @Autowired
    private OpportunityService opportunityService;

    @Autowired
    private OpportunityTeamDivisionService opportunityTeamDivisionService;


    /**
     * 初始化项目成员
     *
     * @param members 用户id+用户角色id+机会id
     * @param tenantId
     */
    @Transactional(rollbackFor = Exception.class)
    public void init(List<OpportunityTeamMember> members,String tenantId)  {
        log.info("OpportunityTeamService init members: {} tenantId: {}", JSON.toJSONString(members),tenantId);

        if(EmptyUtils.isEmpty(members)){
            throw new BusinessException("项目成员不能为空");
        }

        Integer opportunityId = null;
        List<OpportunityTeam> list = new ArrayList<>();
        for(OpportunityTeamMember member: members){
            if(opportunityId==null){
                opportunityId = member.getOpportunityId();
            }
            OpportunityTeam opportunityTeam = buildOpportunityTeam(member,"System",tenantId,true);
            list.add(opportunityTeam);

        }

        if(opportunityId == null){
            throw new BusinessException("项目ID不能为空");
        }

        // 删除项目成员和分工，机会的租户id可能变更，删除时根据机会id删除
        this.mapper.deleteByOpportunityId(opportunityId);

        // 初始化项目成员
        this.batchInsert(list);

        // 初始化项目分工
        this.opportunityTeamDivisionService.init(opportunityId,members,tenantId);
    }

    /**
     * 新增项目成员
     * @param member
     */
    public void add(OpportunityTeamMember member,String createId,String tenantId,boolean isDefault)  {
        if(member == null || member.getUserId()==null || member.getOpportunityId()==null){
            throw new BusinessException("参数不能为空");
        }

        // 判断用户是否重复添加
        Map<String,Object> params = new HashMap<>();
        params.put("userId",member.getUserId());
        params.put("opportunityId",member.getOpportunityId());
        List<OpportunityTeam> exists = this.mapper.selectAll(params);
        if(EmptyUtils.isNotEmpty(exists)){
            throw new BusinessException("用户已经在项目中，请勿重复添加！");
        }

        OpportunityTeam opportunityTeam = buildOpportunityTeam(member,createId,tenantId,isDefault);

        this.mapper.insert(opportunityTeam);
    }

    /**
     *
     * @param members
     * @param updateId
     * @param tenantId
     * @param changeRemark
     */
    @RecordOperation(
            businessType = "OpportunityTeam",
            operationType = "OPPORTUNITY_CHANGE_MANAGER",
            changeHandler = "OpportunityManagerChangeHandler",
            needChangeRemark = true,
            remarkParamIndex = 4  // 变更说明参数索引
    )
    public void changeManager(List<OpportunityTeamMember> members,String updateId,String tenantId,String changeRemark){
        log.info("changeManager {}",JSON.toJSONString(members));
        if(EmptyUtils.isEmpty(members)){
            throw new BusinessException("参数不能为空");
        }

        for (OpportunityTeamMember member: members){
            if(!(member.getRoleType() == RoleTypeEnum.BRANCH_PM.getCode()
                    || member.getRoleType() == RoleTypeEnum.HEAD_PM.getCode()
                    ||member.getRoleType()==RoleTypeEnum.BRANCH_CLERK.getCode())){
                throw new BusinessException("非项目经理的角色类型");
            }

            OpportunityTeam teamManager = getManager(member.getOpportunityId());
            if(teamManager == null){
                // 删除项目经理
                remove(teamManager.getId(),updateId);
            }

            add(member,updateId,tenantId,true);
        }
    }


    /**
     * 根据机会id获取项目经理
     * @param opportunityId
     */
    public OpportunityTeam getManager(Integer opportunityId){
        OpportunityTeam team = this.mapper.selectManager(opportunityId);
        return team;
    }

    /**
     * 根据机会机会ID列表获取项目经理
     *
     * @param opportunityIds 机会ID列表
     * @return 机会团队列表
     */
    public List<OpportunityTeam> getManagers(List<Integer> opportunityIds) {
        return this.mapper.selectManagers(opportunityIds);
    }

    /**
     * 修改成员角色
     *
     * @param member
     * @param updateId
     * @param tenantId
     */
    public void updateMemberRole(OpportunityTeamMember member,String updateId,String tenantId)  {
        if(member == null || member.getUserId()==null || member.getOpportunityId()==null || member.getRoleType()==null){
            throw new BusinessException("参数不能为空");
        }

        OpportunityTeam opportunityTeam = new OpportunityTeam();
        opportunityTeam.setRoleType(member.getRoleType());
        opportunityTeam.setUpdateId(updateId);
        opportunityTeam.setUpdateTime(new Date());
        opportunityTeam.setOpportunityId(member.getOpportunityId());
        opportunityTeam.setUserId(member.getUserId());
        opportunityTeam.setTenantId(tenantId);

        this.mapper.updateMember(opportunityTeam);
    }

    /**
     * 构造成员对象
     *
     * @param member 用户id+角色id
     * @param createId
     * @param tenantId
     * @param isDefault
     * @return
     */
    private OpportunityTeam buildOpportunityTeam(OpportunityTeamMember member,String createId,String tenantId,boolean isDefault){
        OpportunityTeam opportunityTeam = new OpportunityTeam();
        opportunityTeam.setUserId(member.getUserId());
        opportunityTeam.setRoleType(member.getRoleType());
        opportunityTeam.setOpportunityId(member.getOpportunityId());

        if(isDefault){
            opportunityTeam.setJoinType(1);
            opportunityTeam.setIsDefault(1);
        } else {
            opportunityTeam.setJoinType(0);
            opportunityTeam.setIsDefault(0);
        }

        opportunityTeam.setTenantId(tenantId);
        opportunityTeam.setTimes(0);
        opportunityTeam.setCreateId(createId);
        opportunityTeam.setCreateTime(new Date());
        opportunityTeam.setIsDeleted(0);

        return opportunityTeam;
    }

    /**
     * 删除项目成员
     *
     * @param id
     * @param updateId
     */
    public void remove(Integer id,String updateId)  {
        OpportunityTeam opportunityTeam = new OpportunityTeam();
        opportunityTeam.setId(id);
        opportunityTeam.setIsDeleted(1);
        opportunityTeam.setUpdateTime(new Date());
        opportunityTeam.setUpdateId(updateId);

        this.mapper.updateByPrimaryKeySelective(opportunityTeam);
    }

    /**
     *  接受或拒绝邀请
     *
     * @param opportunityId
     * @param joinType
     * @param updateId
     */
    public void acceptOrReject(Integer opportunityId,Integer joinType,String updateId,String tenantId)  {
        OpportunityTeam opportunityTeam = new OpportunityTeam();
        opportunityTeam.setUpdateId(updateId);
        opportunityTeam.setUpdateTime(new Date());
        opportunityTeam.setOpportunityId(opportunityId);
        opportunityTeam.setUserId(updateId);
        opportunityTeam.setTenantId(tenantId);

        this.mapper.updateMember(opportunityTeam);

    }

    /**
     * 确认邀请
     *
     * @param id
     * @param updateId
     * @param manager 确认邀请只有项目经理能操作，默认当前用户为项目经理
     */
    public void inviteNotice(Integer id,String manager, String tenantId,String updateId,String appCode)  {
        if(id == null){
            throw new BusinessException("参数不能为空");
        }
        OpportunityTeamMember member =  findOneMember(id, tenantId);
        if(member == null){
            throw new BusinessException("机会项目成员不存在");
        }
        // 发送邮件通知
        Opportunity opportunity= opportunityService.selectByPrimaryKey(member.getOpportunityId());
        if(opportunity == null){
            throw new BusinessException("未找到机会项目");
        }

        // 发送邀请邮件
        this.sendMail(tenantId,member.getEmail(),member.getNickname(),opportunity.getOpportunityName(),manager,appCode);

        // 修改邀请确认次数
        OpportunityTeam opportunityTeam = new OpportunityTeam();
        opportunityTeam.setId(id);
        opportunityTeam.setTimes(member.getTimes()+1);
        opportunityTeam.setUpdateTime(new Date());
        opportunityTeam.setUpdateId(updateId);

        this.mapper.updateByPrimaryKeySelective(opportunityTeam);
    }

    /**
     * 发送确认邀请邮件
     *
     * @param tenantId
     * @param email 成员邮箱
     * @param name 成员姓名
     * @param oppName 项目名
     * @param manager 项目经理名
     */
    private void sendMail(String tenantId,String email,String name,String oppName,String manager,String appCode){

        String content = buildInviteMsg(name,oppName,manager);
        MailRequest mailRequest = MailRequest.builder()
                .tenantId(tenantId)
                .appCode(appCode)
                .title("项目组队邀请")
                .mailTo(email)
                .content(content)
                .build();

        appSmsWebService.sendMail(mailRequest);
    }

    private String buildInviteMsg(String name,String oppName,String manager){
        return name+"您好，"+oppName+"项目组已成立，项目经理"
                +manager+"邀请您作为本项目的专家，请确认是否接受邀请？望回复，如有疑问可直接与项目经理"
                +manager+"联系，感谢！";
    }

    /**
     * 查询机会项目成员
     *
     * @param opportunityId
     * @param tenantId
     * @return
     */
    public List<OpportunityTeamMember> selectMember(Integer opportunityId, String tenantId){
        if(opportunityId == null){
            throw new BusinessException("机会id不能为空");
        }

        List<OpportunityTeamMember> list = this.mapper.selectMember(opportunityId, tenantId);
        if(EmptyUtils.isNotEmpty(list)){
            // 根据角色类型获取角色类型名称
            for(OpportunityTeamMember member:list){
                member.setRoleName(RoleTypeEnum.getByCode(member.getRoleType()).getName());
            }
        }

        return list;
    }

    /**
     * 根据id查找项目成员
     *
     * @param id
     * @param tenantId
     * @return
     */
    public OpportunityTeamMember findOneMember(Integer id, String tenantId){
        OpportunityTeamMember member =   this.mapper.findOneMember(id,null,null,tenantId);
        return member;
    }


    /**
     * 根据角色查找项目成员
     *
     * @param roleType  角色id
     * @param opportunityId 机会项目id
     * @param tenantId
     * @return 仅返回一个成员
     */
    public OpportunityTeamMember findOneMember(Integer roleType,Integer opportunityId, String tenantId){
        OpportunityTeamMember member =   this.mapper.findOneMember(null,roleType,opportunityId,tenantId);
        return member;
    }

    /**
     * 查询待参与的机会ID列表
     * 查询在项目团队中属于专家角色且join_type为待确认（0）的机会ID列表
     *
     * @param param 查询参数
     * @return 机会ID列表
     */
    public List<Integer> selectPendingParticipationOpportunityIds(Map<String, Object> param) {
        return this.mapper.selectPendingParticipationOpportunityIds(param);
    }

    /**
     * 分页查询待参与的机会ID列表
     * 查询在项目团队中属于专家角色且join_type为待确认（0）的机会ID列表（支持分页）
     *
     * @param param 查询参数
     * @return 机会ID列表
     */
    public List<Integer> selectPendingParticipationOpportunityIdsPage(Map<String, Object> param) {
        return this.mapper.selectPendingParticipationOpportunityIdsPage(param);
    }

}
