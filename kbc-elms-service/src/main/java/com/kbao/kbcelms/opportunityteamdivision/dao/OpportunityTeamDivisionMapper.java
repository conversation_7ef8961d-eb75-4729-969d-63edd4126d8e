package com.kbao.kbcelms.opportunityteamdivision.dao;

import java.util.*;
import java.util.Map;
import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.opportunityteamdivision.entity.OpportunityTeamDivision;
import com.kbao.kbcelms.opportunityteamdivision.model.OpportunityTeamDivisionVO;
import org.apache.ibatis.annotations.Param;

public interface OpportunityTeamDivisionMapper  extends BaseMapper<OpportunityTeamDivision, Integer>{

    /**
     * 查找项目分工
     *
     * @param tenantId
     * @param opportunityId
     * @return
     */
    List<OpportunityTeamDivisionVO> findDivision(@Param("tenantId") String tenantId, @Param("opportunityId") Integer opportunityId,@Param("num") Integer num,@Param("divisionId") String divisionId,@Param("userId") String userId);

    /**
     * 查找单个项目分工
     *
     * @param tenantId
     * @param id
     * @return
     */
    OpportunityTeamDivisionVO findOneDivision(@Param("tenantId") String tenantId,@Param("id") Integer id);


    /**
     * 根据机会id删除项目成员分工比例
     *
     * @param opportunityId
     */
    void deleteByOpportunityId(@Param("opportunityId") Integer opportunityId);
}