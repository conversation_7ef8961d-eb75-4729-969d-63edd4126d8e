package com.kbao.kbcelms.role.service;


import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcelms.auth.entity.Auth;
import com.kbao.kbcelms.auth.service.AuthService;
import com.kbao.kbcelms.auth.vo.AuthRequestVO;
import com.kbao.kbcelms.auth.vo.AuthTreeVO;
import com.kbao.kbcelms.role.vo.RoleAddVO;
import com.kbao.kbcelms.role.vo.RoleRequestVO;
import com.kbao.kbcelms.role.vo.RoleResponseVO;
import com.kbao.kbcelms.roleauth.entity.RoleAuth;
import com.kbao.kbcelms.roleauth.service.RoleAuthService;
import com.kbao.kbcelms.roleauth.vo.RoleAuthAddVO;
import com.kbao.kbcelms.roleauth.vo.RoleAuthRequestVO;
import com.kbao.kbcelms.user.vo.UserResponseVO;
import com.kbao.kbcums.appsms.enums.IsDeleteEnum;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.MapUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.role.entity.Role;
import com.kbao.kbcelms.role.dao.RoleMapper;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;


@Service
public class RoleService extends BaseSQLServiceImpl<Role, Integer, RoleMapper> {

    @Autowired
    private AuthService authService;

    @Autowired
    private RoleAuthService roleAuthService;

    /**
     * 排序
     *
     * @param null
     * <AUTHOR>
     * @Date 2025/7/22 16:02
     */
    private Comparator<AuthTreeVO> authCompare = new Comparator<AuthTreeVO>() {
        @Override
        public int compare(AuthTreeVO o1, AuthTreeVO o2) {
            if (EmptyUtils.isEmpty(o1.getSort())) {
                o1.setSort(999999999);
            }
            if (EmptyUtils.isEmpty(o2.getSort())) {
                o2.setSort(999999999);
            }
            return o1.getSort() - o2.getSort();
        }
    };

    /**
     * 角色列表分页查询
     *
     * @param pageRequest
     * <AUTHOR>
     * @Date 2025/7/22 14:00
     */
    public PageInfo<RoleResponseVO> pageRole(PageRequest<RoleRequestVO> pageRequest) {
        Map<String, Object> queryParam = MapUtils.objectToMap(pageRequest.getParam());
        if (queryParam == null) {
            queryParam = new ConcurrentHashMap<>(16);
        }
        PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
//        Page<RoleResponseVO> page = (Page<RoleResponseVO>) mapper.findByCondition(queryParam);
        Page<Role> page = (Page<Role>) mapper.selectAll(queryParam);

        PageInfo<RoleResponseVO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(page, pageInfo);

        List<RoleResponseVO> list = new ArrayList();

        page.getResult().forEach(role -> {
            RoleResponseVO responseVO = new RoleResponseVO();
            BeanUtils.copyProperties(role, responseVO);
            list.add(responseVO);
        });
        pageInfo.setList(list);
        return pageInfo;
    }

    /**
     * 租户下所有角色
     *
     * @param
     * <AUTHOR>
     * @Date 2025/7/24 17:20
     */
    public List<RoleResponseVO> list() {
        Map<String, Object> queryParam = Maps.newHashMap();
        queryParam.put("tenantId", SysLoginUtils.getUser().getTenantId());
        List<Role> list = mapper.selectAll(queryParam);
        List<RoleResponseVO> result = new ArrayList();

        list.forEach(role -> {
            RoleResponseVO responseVO = new RoleResponseVO();
            BeanUtils.copyProperties(role, responseVO);
            result.add(responseVO);
        });
        return result;
    }

    /**
     * 角色详情
     *
     * @param id
     * <AUTHOR>
     * @Date 2025/7/22 14:05
     */
    public RoleResponseVO detail(Integer id) {
        Role role = mapper.selectByPrimaryKey(id);
        RoleResponseVO responseVO = new RoleResponseVO();
        BeanUtils.copyProperties(role, responseVO);
        return responseVO;
    }

    /**
     * 角色增改
     *
     * @param roleAddVO
     * <AUTHOR>
     * @Date 2025/7/22 14:00
     */
    public void saveOrUpdate(RoleAddVO roleAddVO) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        Role role = mapper.getByRoleName(roleAddVO.getRoleName(), tenantId);
        if (EmptyUtils.isEmpty(roleAddVO.getRoleId())) {
            // 根据角色名称校验是否存在重复
            if (EmptyUtils.isNotEmpty(role)) {
                throw new BusinessException("该角色名称已存在");
            }
            Role newRole = new Role();
            newRole.setRoleName(roleAddVO.getRoleName());
            newRole.setTenantId(tenantId);
            newRole.setRoleType(roleAddVO.getRoleType());
            newRole.setRoleDesc(roleAddVO.getRoleDesc());
            newRole.setCreateId(SysLoginUtils.getUserId());
            newRole.setCreateTime(DateUtils.getCurrentDate());
            newRole.setIsDeleted(IsDeleteEnum.NO.getCode());
            newRole.setSort(this.getSort(tenantId));
            newRole.setUpdateId(newRole.getCreateId());
            newRole.setUpdateTime(newRole.getCreateTime());
            mapper.insert(newRole);
        } else {
            if (EmptyUtils.isNotEmpty(role) && role.getId() != roleAddVO.getRoleId()) {
                throw new BusinessException("该角色名称已存在");
            }
            Role oldRole = new Role();
            oldRole.setId(roleAddVO.getRoleId());
            oldRole.setRoleName(roleAddVO.getRoleName());
            oldRole.setRoleDesc(roleAddVO.getRoleDesc());
            oldRole.setUpdateId(SysLoginUtils.getUserId());
            oldRole.setUpdateTime(DateUtils.getCurrentDate());
            mapper.updateByPrimaryKeySelective(oldRole);
        }
    }

    /**
     * 角色删除
     *
     * @param roleId
     * <AUTHOR>
     * @Date 2025/7/22 14:04
     */
    public void deleteById(Integer roleId) {
        Role role = new Role();
        role.setId(roleId);
        role.setIsDeleted(IsDeleteEnum.YES.getCode());
        role.setUpdateId(SysLoginUtils.getUserId());
        role.setUpdateTime(DateUtils.getCurrentDate());
        mapper.updateByPrimaryKeySelective(role);
    }

    /**
     * 获取角色排序序号
     *
     * @param tenantId
     * <AUTHOR>
     * @Date 2025/7/22 14:00
     */
    public int getSort(String tenantId) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("tenantId", tenantId);
        List<Role> list = mapper.selectAll(paramMap);
        if (EmptyUtils.isEmpty(list)) {
            return 1;
        }
        return list.size() + 1;
    }

    /**
     * 获取权限编码树结构数据
     *
     * @param
     * <AUTHOR>
     * @Date 2025/7/22 16:03
     */
    public List<AuthTreeVO> selectAuthTree(AuthRequestVO requestVO) {

        List<String> roleAuthCodes = new ArrayList<>();
        List<String> roleAuthPaths = new ArrayList<>();
        // 查出当前角色已选择的权限位
        if (EmptyUtils.isNotEmpty(requestVO.getRoleId())) {
            List<RoleAuth> roleAuths = roleAuthService.findByRoleId(requestVO.getRoleId());
            if (EmptyUtils.isNotEmpty(roleAuths)) {
                roleAuthPaths.addAll(roleAuths.stream().map(RoleAuth::getAuthPath).collect(Collectors.toList()));
            }
        }

        // 选中节点及其父节点权限位ID 去重
        if (EmptyUtils.isNotEmpty(roleAuthPaths)) {
            List<String> tempAuthCodes = new ArrayList<>();
            roleAuthPaths.forEach(authPath -> {
                for (String authCode : authPath.split("/")) {
                    tempAuthCodes.add(authCode);
                }
            });
            roleAuthCodes = tempAuthCodes.stream().distinct().collect(Collectors.toList());
        }

        List<AuthTreeVO> treeList = new ArrayList<>();
        List<Auth> allList = authService.findAll();
        // 权限位树结构化以及默认选中状态赋值
        if (EmptyUtils.isNotEmpty(allList)) {
            for (Auth auth : allList) {
                if (EmptyUtils.isEmpty(auth.getParentCode())) {
                    AuthTreeVO treeVO = new AuthTreeVO();
                    treeVO.setId(auth.getId());
                    treeVO.setAuthCode(auth.getAuthCode());
                    treeVO.setAuthName(auth.getAuthName());
                    treeVO.setSort(auth.getSort());
                    treeVO.setIsSelected(roleAuthCodes.contains(auth.getAuthCode()));
                    treeList.add(treeVO);
                }
            }
            // 排序
            Collections.sort(treeList, authCompare);

            if (EmptyUtils.isNotEmpty(treeList)) {
                for (AuthTreeVO treeData : treeList) {
                    this.setAuthTreeChildren(treeData, allList, roleAuthCodes);
                }
            }
        }
        return treeList;
    }

    /**
     * 递归获取子权限编码
     *
     * @param authData
     * @param authTrees
     * <AUTHOR>
     * @Date 2025/7/22 16:02
     */
    public void setAuthTreeChildren(AuthTreeVO authData, List<Auth> authTrees, List<String> roleAuthCodes) {
        List<AuthTreeVO> treeDataList = new ArrayList<>();
        for (Auth auth : authTrees) {
            if (EmptyUtils.isNotEmpty(auth.getParentCode())
                    && auth.getParentCode().equals(authData.getAuthCode())) {

                AuthTreeVO treeVO = new AuthTreeVO();
                treeVO.setId(auth.getId());
                treeVO.setAuthCode(auth.getAuthCode());
                treeVO.setAuthName(auth.getAuthName());
                treeVO.setParentCode(auth.getParentCode());
                treeVO.setSort(auth.getSort());
                treeVO.setIsSelected(roleAuthCodes.contains(auth.getAuthCode()));
                this.setAuthTreeChildren(treeVO, authTrees, roleAuthCodes);
                treeDataList.add(treeVO);
            }
        }
        // 排序
        Collections.sort(treeDataList, authCompare);
        authData.setChildren(treeDataList);
    }

    /**
     * 角色权限位配置
     *
     * @param addVO
     * <AUTHOR>
     * @Date 2025/7/23 10:31
     */
    public void saveOrUpdateRoleAuth(RoleAuthAddVO addVO) {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        if (EmptyUtils.isNotEmpty(addVO.getRoleId())) {
            roleAuthService.deleteByRoleId(addVO.getRoleId());
        }
        List<RoleAuth> list = new ArrayList<>();
        List<RoleAuthRequestVO> auths = addVO.getAuths();
        int sort = 1;
        for (RoleAuthRequestVO auth : auths) {
            RoleAuth roleAuth = new RoleAuth();
            roleAuth.setAuthCode(auth.getAuthCode());
            roleAuth.setAuthPath(auth.getAuthPath());
            roleAuth.setRoleName(addVO.getRoleName());
            roleAuth.setRoleId(addVO.getRoleId());
            roleAuth.setTenantId(tenantId);
            roleAuth.setCreateId(SysLoginUtils.getUserId());
            roleAuth.setCreateTime(DateUtils.getCurrentDate());
            roleAuth.setUpdateId(SysLoginUtils.getUserId());
            roleAuth.setUpdateTime(DateUtils.getCurrentDate());
            roleAuth.setSort(sort);
            roleAuth.setIsDeleted(IsDeleteEnum.NO.getCode());
            list.add(roleAuth);
            sort++;
        }
        roleAuthService.batchInsert(list);
    }

    /**
     * 根据用户ID查询所有角色
     *
     * @param userId 用户ID
     * @return 角色列表
     * <AUTHOR>
     * @Date 2025/1/27 10:00
     */
    public List<Role> getRolesByUserId(String userId) {
        if (EmptyUtils.isEmpty(userId)) {
            return new ArrayList<>();
        }
        return mapper.selectRolesByUserId(userId);
    }

}
