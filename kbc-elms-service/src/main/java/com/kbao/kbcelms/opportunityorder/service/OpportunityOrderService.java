package com.kbao.kbcelms.opportunityorder.service;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcbsc.util.BscUserUtils;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import org.springframework.stereotype.Service;
import com.kbao.kbcelms.opportunityorder.entity.OpportunityOrder;
import com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper;

/**
 * 机会订单服务
 * @luobb
 */
@Service
public class OpportunityOrderService extends BaseSQLServiceImpl<OpportunityOrder, Integer, OpportunityOrderMapper> {

    /**
     * 新增机会保单
     * @param opportunityOrder
     * @return
     */
    public OpportunityOrder addOpportunityOrder(OpportunityOrder opportunityOrder) {
        // 检查保单是否唯一
        int count = this.mapper.selectCountByUniqueParams(opportunityOrder);
        if (count > 0) {
            throw new BusinessException("保单[" + opportunityOrder.getPolicyNo() + "]已存在");
        }
        initOpportunityOrder(opportunityOrder);
        insert(opportunityOrder);
        return opportunityOrder;
    }

    private void initOpportunityOrder(OpportunityOrder opportunityOrder){
        String tenantId = SysLoginUtils.getUser().getTenantId();
        opportunityOrder.setCreateId(BscUserUtils.getUserId());
        opportunityOrder.setCreateTime(DateUtils.getCurrentDate());
        opportunityOrder.setUpdateId(BscUserUtils.getUserId());
        opportunityOrder.setUpdateTime(DateUtils.getCurrentDate());
        opportunityOrder.setTenantId(tenantId);
    }

    /**
     * 修改机会保单
     * @param opportunityOrder
     * @return
     */
    public OpportunityOrder updateOpportunityOrder(OpportunityOrder opportunityOrder) {
        OpportunityOrder existingOrder = this.selectByPrimaryKey(opportunityOrder.getId());

        if (existingOrder == null) {
           throw new BusinessException("机会保单不存在");
        }

        if(existingOrder.getIsDeleted() == 1) {
            throw new BusinessException("机会保单已删除");
        }

        // 检查保单是否唯一
        int count = this.mapper.selectCountByUniqueParams(opportunityOrder);
        if (count > 0) {
            throw new BusinessException("保单[" + opportunityOrder.getPolicyNo() + "]已存在");
        }

        existingOrder.setOrderCode(opportunityOrder.getOrderCode());
        existingOrder.setPolicyNo(opportunityOrder.getPolicyNo());
        existingOrder.setCompanyCode(opportunityOrder.getCompanyCode());
        existingOrder.setCompanyName(opportunityOrder.getCompanyName());
        existingOrder.setUpdateId(BscUserUtils.getUserId());
        existingOrder.setUpdateTime(DateUtils.getCurrentDate());
        updateByPrimaryKey(existingOrder);
        return existingOrder;
    }

    /**
     * 删除机会保单
     * @param id
     */
    public void deleteOpportunityOrder(Integer id) {
        OpportunityOrder existingOrder = this.selectByPrimaryKey(id);
        if (existingOrder == null) {
            throw new BusinessException("机会保单不存在");
        }
        existingOrder.setIsDeleted(1);
        existingOrder.setUpdateId(BscUserUtils.getUserId());
        existingOrder.setUpdateTime(DateUtils.getCurrentDate());
        updateByPrimaryKey(existingOrder);
    }

    /**
     * 根据机会id查询机会保单列表
     * @param opportunityId
     * @return
     */
    public List<OpportunityOrder> queryOpportunityOrderListByOpportunityId(String opportunityId) {
        Map<String, Object> param = new HashMap<>();
        param.put("opportunityId", opportunityId);
        param.put("isDeleted", 0);
        return selectByParam(param);
    }


    /**
     * 批量新增机会保单
     * @param opportunityOrders
     * @return
     */
    public String batchAddOpportunityOrder(List<OpportunityOrder> opportunityOrders) {

        if(EmptyUtils.isEmpty(opportunityOrders)){
            return null;
        }

        StringBuffer sb = new StringBuffer();
        for (OpportunityOrder opportunityOrder : opportunityOrders) {
            // 检查保单是否唯一
            int count = this.mapper.selectCountByUniqueParams(opportunityOrder);
            if (count > 0) {
                sb.append("保单[" + opportunityOrder.getPolicyNo() + "]已存在;");
            }
            else {
                initOpportunityOrder(opportunityOrder);
                insert(opportunityOrder);
            }
        }

        return sb.toString();
    }
}
