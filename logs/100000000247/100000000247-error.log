[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:31:35.516 ERROR 19636 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Tue Aug 12 09:31:36 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:31:35.616 ERROR 19636 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Tue Aug 12 09:31:36 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:31:35.679 ERROR 19636 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Tue Aug 12 09:31:36 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7003] [54792ee6394ae4de,] 2025-08-12 09:47:44.575 ERROR 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求报错，transId=54792ee6394ae4de,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} ,error =
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
### The error may exist in file [D:\idea_workspace\kbc-elms\kbc-elms\kbc-elms-entity\target\classes\com\kbao\kbcelms\opportunity\entity\OpportunityMapper.xml]
### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline
### The error occurred while setting parameters
### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list' 
[kbc-elms-web:10.78.8.1:7003] [54792ee6394ae4de,] 2025-08-12 09:47:44.591 ERROR 19636 [http-nio-7003-exec-10] com.kbao.kbcelms.web.handler.ElmsGlobalExceptionHandler 运行时异常 - 请求URI: /api/agentEnterprise/stat, 异常信息: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
### The error may exist in file [D:\idea_workspace\kbc-elms\kbc-elms\kbc-elms-entity\target\classes\com\kbao\kbcelms\opportunity\entity\OpportunityMapper.xml]
### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline
### The error occurred while setting parameters
### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'

org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
### The error may exist in file [D:\idea_workspace\kbc-elms\kbc-elms\kbc-elms-entity\target\classes\com\kbao\kbcelms\opportunity\entity\OpportunityMapper.xml]
### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline
### The error occurred while setting parameters
### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy197.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy253.statAgentEnterprise(Unknown Source)
	at com.kbao.kbcelms.opportunity.service.OpportunityService.statAgentEnterprise(OpportunityService.java:777)
	at com.kbao.kbcelms.opportunity.service.OpportunityService$$FastClassBySpringCGLIB$$e7a290c7.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.kbao.kbcelms.opportunity.service.OpportunityService$$EnhancerBySpringCGLIB$$faf51b61.statAgentEnterprise(<generated>)
	at com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController.statAgentEnterprise(GenAgentEnterpriseController.java:31)
	at com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController$$FastClassBySpringCGLIB$$6d364ae9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.kbao.kbcbsc.log.aop.LogAnnotationAOP.logSave(LogAnnotationAOP.java:135)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController$$EnhancerBySpringCGLIB$$20fffbe6.statAgentEnterprise(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.kbao.commons.filter.TraceContextFilter.doFilterInternal(TraceContextFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy517.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:63)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.util.ExecutorUtil.executeAutoCount(ExecutorUtil.java:138)
	at com.github.pagehelper.PageInterceptor.count(PageInterceptor.java:148)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:97)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy515.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 90 common frames omitted

[kbc-elms-web:10.78.8.1:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.154 ERROR 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求报错，transId=91d1ed891d0f0fa8,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} ,error =
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
### The error may exist in file [D:\idea_workspace\kbc-elms\kbc-elms\kbc-elms-entity\target\classes\com\kbao\kbcelms\opportunity\entity\OpportunityMapper.xml]
### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline
### The error occurred while setting parameters
### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list' 
[kbc-elms-web:10.78.8.1:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.157 ERROR 19636 [http-nio-7003-exec-5] com.kbao.kbcelms.web.handler.ElmsGlobalExceptionHandler 运行时异常 - 请求URI: /api/agentEnterprise/stat, 异常信息: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
### The error may exist in file [D:\idea_workspace\kbc-elms\kbc-elms\kbc-elms-entity\target\classes\com\kbao\kbcelms\opportunity\entity\OpportunityMapper.xml]
### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline
### The error occurred while setting parameters
### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'

org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
### The error may exist in file [D:\idea_workspace\kbc-elms\kbc-elms\kbc-elms-entity\target\classes\com\kbao\kbcelms\opportunity\entity\OpportunityMapper.xml]
### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline
### The error occurred while setting parameters
### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:73)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy197.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy253.statAgentEnterprise(Unknown Source)
	at com.kbao.kbcelms.opportunity.service.OpportunityService.statAgentEnterprise(OpportunityService.java:777)
	at com.kbao.kbcelms.opportunity.service.OpportunityService$$FastClassBySpringCGLIB$$e7a290c7.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.kbao.kbcelms.opportunity.service.OpportunityService$$EnhancerBySpringCGLIB$$faf51b61.statAgentEnterprise(<generated>)
	at com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController.statAgentEnterprise(GenAgentEnterpriseController.java:31)
	at com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController$$FastClassBySpringCGLIB$$6d364ae9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.kbao.kbcbsc.log.aop.LogAnnotationAOP.logSave(LogAnnotationAOP.java:135)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController$$EnhancerBySpringCGLIB$$20fffbe6.statAgentEnterprise(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.kbao.commons.filter.TraceContextFilter.doFilterInternal(TraceContextFilter.java:51)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy517.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:63)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.util.ExecutorUtil.executeAutoCount(ExecutorUtil.java:138)
	at com.github.pagehelper.PageInterceptor.count(PageInterceptor.java:148)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:97)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy515.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 90 common frames omitted

[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:10.820 ERROR 20568 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Tue Aug 12 09:49:11 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:142)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:10.873 ERROR 20568 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Tue Aug 12 09:49:11 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:145)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:10.923 ERROR 20568 [main] com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder get data from Nacos error,dataId:sta-kbcs-app-elms-web-jar-dev.yml, 

com.alibaba.nacos.api.exception.NacosException: <html><body><h1>Whitelabel Error Page</h1><p>This application has no explicit mapping for /error, so you are seeing this as a fallback.</p><div id='created'>Tue Aug 12 09:49:11 CST 2025</div><div>There was an unexpected error (type=Forbidden, status=403).</div><div>authorization failed!</div></body></html>
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:239)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:160)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:100)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:74)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:204)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:191)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:150)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:95)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:639)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at com.kbao.kbcelms.KbcElmsWebApplication.main(KbcElmsWebApplication.java:32)

