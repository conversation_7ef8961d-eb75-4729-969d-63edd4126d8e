[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:01:14.426 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:06:14.438 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:11:14.443 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:16:14.453 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:21:14.455 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:26:14.468 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:31:14.474 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:36:14.479 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:41:14.494 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:46:14.504 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:51:14.506 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:56:14.519 INFO 12080 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:56:16.876 WARN 12080 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:56:16.888 INFO 12080 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[kbc-elms-web:10.78.8.1:7002] [,] 2025-08-12 08:56:16.914 WARN 12080 [Thread-8] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
