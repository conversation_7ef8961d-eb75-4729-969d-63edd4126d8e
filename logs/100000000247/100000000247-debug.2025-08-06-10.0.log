[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.640 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.640 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.641 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 2c02ad566419c161
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.641 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 19fa87906c2eae6b
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.641 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.642 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.642 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.642 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.642 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.643 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: *********9
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.643 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.643 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.643 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.643 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"3be3f792cb000614a02e65e6f3f8a7b1","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.643 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.643 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (141ms)
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Wed, 06 Aug 2025 02:18:44 GMT
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.785 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.786 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [2c02ad566419c161,] 2025-08-06 10:18:43.786 DEBUG 12080 [http-nio-7002-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.795 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (151ms)
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.795 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.796 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.796 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Wed, 06 Aug 2025 02:18:44 GMT
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.796 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.796 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.796 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.797 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.797 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"resp_code":401,"resp_msg":"用户登录的Token无效，请重新登录快保云服"}
[kbc-elms-web:*********:7002] [19fa87906c2eae6b,] 2025-08-06 10:18:43.797 DEBUG 12080 [http-nio-7002-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (87-byte body)
