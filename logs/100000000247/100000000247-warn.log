[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:31:45.329 WARN 19636 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:31:55.283 WARN 19636 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:31:56.151 WARN 19636 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:32:12.953 WARN 19636 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:32:18.741 WARN 19636 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:48:59.928 WARN 19636 [Thread-11] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:48:59.933 WARN 19636 [Thread-11] com.alibaba.nacos.common.http.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:17.702 WARN 20568 [main] org.springframework.boot.actuate.endpoint.EndpointId Endpoint ID 'nacos-config' contains invalid characters, please migrate to a valid format.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:26.515 WARN 20568 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:27.246 WARN 20568 [main] org.springframework.data.convert.CustomConversions Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:40.859 WARN 20568 [main] org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[kbc-elms-web:10.78.8.1:7003] [,] 2025-08-12 09:49:46.138 WARN 20568 [main] springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader Trying to infer dataType java.lang.String[]
