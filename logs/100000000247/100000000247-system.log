{"@timestamp":"2025-08-12T09:47:30.515+08:00","traceId":"40d0b1b478e84c97","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963249296,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1218,\"traceId\":\"40d0b1b478e84c97\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:34.934+08:00","traceId":"d6c0fb44b59ad5bb","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963254870,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":63,\"traceId\":\"d6c0fb44b59ad5bb\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:35.732+08:00","traceId":"0c4731b624e3b6c1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963255686,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":44,\"traceId\":\"0c4731b624e3b6c1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:36.959+08:00","traceId":"4c7f1927eadda760","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963256212,\"desc\":\"分页查询企业基本信息列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page\",\"module\":\"企业信息管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"actualCapital\\\":\\\"20217.5636万人民币\\\",\\\"actualCapitalCurrency\\\":\\\"人民币\\\",\\\"alias\\\":\\\"百达精工\\\",\\\"approvedTime\\\":1749744000000,\\\"base\\\":\\\"zj\\\",\\\"bondName\\\":\\\"百达精工\\\",\\\"bondNum\\\":\\\"603331\\\",\\\"bondType\\\":\\\"A股\\\",\\\"businessScope\\\":\\\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\\\",\\\"city\\\":\\\"台州市\\\",\\\"companyId\\\":44302802,\\\"companyOrgType\\\":\\\"其他股份有限公司(上市)\\\",\\\"createTime\\\":1754011649790,\\\"creditCode\\\":\\\"913310007200456372\\\",\\\"district\\\":\\\"椒江区\\\",\\\"districtCode\\\":\\\"331002\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"emailList\\\":[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"],\\\"fromTime\\\":965577600000,\\\"historyNameList\\\":[\\\"台州市百达制冷有限公司\\\"],\\\"historyNames\\\":\\\"台州市百达制冷有限公司;\\\",\\\"id\\\":\\\"688c1802bf13d11683ba16ba\\\",\\\"industry\\\":\\\"通用设备制造业\\\",\\\"industryAll\\\":{\\\"category\\\":\\\"制造业\\\",\\\"categoryBig\\\":\\\"通用设备制造业\\\",\\\"categoryCodeFirst\\\":\\\"C\\\",\\\"categoryCodeSecond\\\":\\\"34\\\",\\\"categoryCodeThird\\\":\\\"346\\\",\\\"categoryMiddle\\\":\\\"烘炉、风机、包装等设备制造\\\",\\\"categorySmall\\\":\\\"\\\"},\\\"isMicroEnt\\\":0,\\\"legalPersonName\\\":\\\"施小友\\\",\\\"name\\\":\\\"浙江百达精工股份有限公司\\\",\\\"orgNumber\\\":\\\"72004563-7\\\",\\\"percentileScore\\\":9267,\\\"phoneNumber\\\":\\\"0576-88488860\\\",\\\"property3\\\":\\\"Zhejiang Baida Precision Manufacturing Corp.\\\",\\\"regCapital\\\":\\\"20217.5636万人民币\\\",\\\"regCapitalCurrency\\\":\\\"人民币\\\",\\\"regInstitute\\\":\\\"浙江省市场监督管理局\\\",\\\"regLocation\\\":\\\"浙江省台州市台州湾新区海城路2399号\\\",\\\"regNumber\\\":\\\"331000000000905\\\",\\\"regStatus\\\":\\\"存续\\\",\\\"scale\\\":\\\"大型\\\",\\\"socialStaffNum\\\":1246,\\\"staffNumRange\\\":\\\"1000-4999人\\\",\\\"tags\\\":\\\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\\\",\\\"taxNumber\\\":\\\"913310007200456372\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"toTime\\\":253392422400000,\\\"type\\\":1,\\\"updateTime\\\":1754033435185,\\\"updateTimes\\\":1752112735000,\\\"usedBondName\\\":\\\"\\\",\\\"websiteList\\\":\\\"http://www.baidapm.com\\\"},{\\\"actualCapital\\\":\\\"185000万人民币\\\",\\\"actualCapitalCurrency\\\":\\\"人民币\\\",\\\"alias\\\":\\\"小米科技\\\",\\\"approvedTime\\\":1732118400000,\\\"base\\\":\\\"bj\\\",\\\"businessScope\\\":\\\"一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口；进出口代理；通讯设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；个人卫生用品销售；卫生用品和一次性使用医疗用品销售；日用杂品销售；日用百货销售；日用品销售；化妆品批发；化妆品零售；第一类医疗器械销售；第二类医疗器械销售；玩具销售；体育用品及器材零售；体育用品及器材批发；文具用品零售；文具用品批发；鞋帽批发；鞋帽零售；服装服饰批发；服装服饰零售；钟表销售；眼镜销售（不含隐形眼镜）；针纺织品销售；家用电器销售；日用家电零售；家具销售；礼品花卉销售；农作物种子经营（仅限不再分装的包装种子）；照相机及器材销售；照相器材及望远镜批发；照相器材及望远镜零售；工艺美术品及收藏品零售（象牙及其制品除外）；工艺美术品及礼仪用品销售（象牙及其制品除外）；计算机软硬件及辅助设备零售；计算机软硬件及辅助设备批发；珠宝首饰零售；珠宝首饰批发；食用农产品批发；食用农产品零售；宠物食品及用品批发；宠物食品及用品零售；电子产品销售；摩托车及零配件零售；摩托车及零配件批发；电动自行车销售；助动自行车、代步车及零配件销售；自行车及零配件零售；自行车及零配件批发；单用途商业预付卡代理销售；商用密码产品销售；五金产品批发；五金产品零售；建筑材料销售；仪器仪表修理；计算机及办公设备维修；办公设备销售；会议及展览服务；组织文化艺术交流活动；广告设计、代理；广告制作；广告发布；摄影扩印服务；票务代理服务；通讯设备修理；移动终端设备制造；可穿戴智能设备制造。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）许可项目：第三类医疗器械经营；网络文化经营；出版物零售；出版物批发；食品销售；药品零售；广播电视节目制作经营；第一类增值电信业务；第二类增值电信业务；在线数据处理与交易处理业务（经营类电子商务）；基础电信业务；互联网信息服务；信息网络传播视听节目。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）（不得从事国家和本市产业政策禁止和限制类项目的经营活动。）\\\",\\\"city\\\":\\\"北京市\\\",\\\"companyId\\\":23402373,\\\"companyOrgType\\\":\\\"有限责任公司(自然人投资或控股)\\\",\\\"createTime\\\":1754035025699,\\\"creditCode\\\":\\\"91110108551385082Q\\\",\\\"district\\\":\\\"海淀区\\\",\\\"districtCode\\\":\\\"110108\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"emailList\\\":[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"],\\\"fromTime\\\":1267545600000,\\\"historyNameList\\\":[\\\"北京小米科技有限责任公司\\\"],\\\"historyNames\\\":\\\"北京小米科技有限责任公司;\\\",\\\"id\\\":\\\"688c7351cc2f9762f84fced7\\\",\\\"industry\\\":\\\"计算机、通信和其他电子设备制造业\\\",\\\"industryAll\\\":{\\\"category\\\":\\\"制造业\\\",\\\"categoryBig\\\":\\\"计算机、通信和其他电子设备制造业\\\",\\\"categoryCodeFirst\\\":\\\"C\\\",\\\"categoryCodeSecond\\\":\\\"39\\\",\\\"categoryCodeThird\\\":\\\"392\\\",\\\"categoryMiddle\\\":\\\"通信设备制造\\\",\\\"categorySmall\\\":\\\"\\\"},\\\"isMicroEnt\\\":1,\\\"legalPersonName\\\":\\\"雷军\\\",\\\"name\\\":\\\"小米科技有限责任公司\\\",\\\"orgNumber\\\":\\\"55138508-2\\\",\\\"percentileScore\\\":9855,\\\"phoneNumber\\\":\\\"010-69630728\\\",\\\"property3\\\":\\\"Beijing Xiaomi Technology Co., Ltd.\\\",\\\"regCapital\\\":\\\"185000万人民币\\\",\\\"regCapitalCurrency\\\":\\\"人民币\\\",\\\"regInstitute\\\":\\\"北京市海淀区市场监督管理局\\\",\\\"regLocation\\\":\\\"北京市海淀区西二旗中路33号院6号楼6层006号\\\",\\\"regNumber\\\":\\\"110108012660422\\\",\\\"regStatus\\\":\\\"存续\\\",\\\"socialStaffNum\\\":37,\\\"staffNumRange\\\":\\\"小于50人\\\",\\\"tags\\\":\\\"存续;曾用名;投资机构;赴港上市(正常上市);小微企业;高新技术企业;独角兽;瞪羚企业;合作风险;竞争风险;司法案件;战略融资\\\",\\\"taxNumber\\\":\\\"91110108551385082Q\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":1,\\\"updateTime\\\":1754035025699,\\\"updateTimes\\\":1753952116000,\\\"websiteList\\\":\\\"https://www.mi.com\\\"}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":746,\"traceId\":\"4c7f1927eadda760\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:43.499+08:00","traceId":"8fb36825e37fc054","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963263425,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":54,\"traceId\":\"8fb36825e37fc054\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:44.576+08:00","traceId":"54792ee6394ae4de","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963263697,\"desc\":\"分页查询企业客户统计\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise\",\"module\":\"企业客户管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"agentCode\\\":\\\"\\\",\\\"agentName\\\":\\\"\\\"}}\",\"remark\":\"\\r\\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'\\r\\n### The error may exist in file [D:\\\\idea_workspace\\\\kbc-elms\\\\kbc-elms\\\\kbc-elms-entity\\\\target\\\\classes\\\\com\\\\kbao\\\\kbcelms\\\\opportunity\\\\entity\\\\OpportunityMapper.xml]\\r\\n### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count\\r\\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'\\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":879,\"traceId\":\"54792ee6394ae4de\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:47.149+08:00","traceId":"81dff1a0412538a9","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963267068,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":47,\"traceId\":\"81dff1a0412538a9\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:47.474+08:00","traceId":"f2eaaf4bc2bf4d8d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963267296,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":5,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\">=500000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":500000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000},{\\\"code\\\":\\\"B\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754471503000,\\\"description\\\":\\\"B类公司\\\",\\\"employeeRangeText\\\":\\\">=50000人\\\",\\\"id\\\":\\\"68931c4f739d9025b7e7a662\\\",\\\"name\\\":\\\"B类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754471503000},{\\\"code\\\":\\\"C\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881954000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=5000人\\\",\\\"id\\\":\\\"68995fa257eb6e16dfaf61dc\\\",\\\"name\\\":\\\"C类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":5000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881954000},{\\\"code\\\":\\\"D\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881995000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=500人\\\",\\\"id\\\":\\\"68995fcb57eb6e16dfaf61dd\\\",\\\"name\\\":\\\"D类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":500}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881995000},{\\\"code\\\":\\\"E\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754882012378,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=50人\\\",\\\"id\\\":\\\"68995fdc57eb6e16dfaf61de\\\",\\\"name\\\":\\\"E类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":50}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754882012378}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":5,\\\"startRow\\\":1,\\\"total\\\":5},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":177,\"traceId\":\"f2eaaf4bc2bf4d8d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:51.005+08:00","traceId":"c520747be448d770","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963270926,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":77,\"traceId\":\"c520747be448d770\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:47:51.155+08:00","traceId":"91d1ed891d0f0fa8","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963271119,\"desc\":\"分页查询企业客户统计\",\"flag\":false,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise\",\"module\":\"企业客户管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"agentCode\\\":\\\"\\\",\\\"agentName\\\":\\\"\\\"}}\",\"remark\":\"\\r\\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'\\r\\n### The error may exist in file [D:\\\\idea_workspace\\\\kbc-elms\\\\kbc-elms\\\\kbc-elms-entity\\\\target\\\\classes\\\\com\\\\kbao\\\\kbcelms\\\\opportunity\\\\entity\\\\OpportunityMapper.xml]\\r\\n### The error may involve com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise-Inline\\r\\n### The error occurred while setting parameters\\r\\n### SQL: select count(0) from (select             op.agent_code agentCode,             op.agent_name agentName,             op.legal_name legalName,             op.sales_center_name salesCenterName,             count(distinct ae.id) agentEnterpriseNum,             sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum,             count(distinct op.id) opportunityNum,             sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum,             sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum         from t_opportunity op             left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id          WHERE op.is_deleted = 0                              and op.tenant_id = ?          group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name         order by op.agent_code) tmp_count\\r\\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'\\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ae.isVerified' in 'field list'\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":36,\"traceId\":\"91d1ed891d0f0fa8\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:49:59.380+08:00","traceId":"c09451d24e972b7d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963398310,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1070,\"traceId\":\"c09451d24e972b7d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:49:59.380+08:00","traceId":"7f271ea68e3b6ddb","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963398310,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1070,\"traceId\":\"7f271ea68e3b6ddb\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:49:59.654+08:00","traceId":"493b7a2f4d694425","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963398362,\"desc\":\"分页查询企业基本信息列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page\",\"module\":\"企业信息管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"actualCapital\\\":\\\"20217.5636万人民币\\\",\\\"actualCapitalCurrency\\\":\\\"人民币\\\",\\\"alias\\\":\\\"百达精工\\\",\\\"approvedTime\\\":1749744000000,\\\"base\\\":\\\"zj\\\",\\\"bondName\\\":\\\"百达精工\\\",\\\"bondNum\\\":\\\"603331\\\",\\\"bondType\\\":\\\"A股\\\",\\\"businessScope\\\":\\\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\\\",\\\"city\\\":\\\"台州市\\\",\\\"companyId\\\":44302802,\\\"companyOrgType\\\":\\\"其他股份有限公司(上市)\\\",\\\"createTime\\\":1754011649790,\\\"creditCode\\\":\\\"913310007200456372\\\",\\\"district\\\":\\\"椒江区\\\",\\\"districtCode\\\":\\\"331002\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"emailList\\\":[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"],\\\"fromTime\\\":965577600000,\\\"historyNameList\\\":[\\\"台州市百达制冷有限公司\\\"],\\\"historyNames\\\":\\\"台州市百达制冷有限公司;\\\",\\\"id\\\":\\\"688c1802bf13d11683ba16ba\\\",\\\"industry\\\":\\\"通用设备制造业\\\",\\\"industryAll\\\":{\\\"category\\\":\\\"制造业\\\",\\\"categoryBig\\\":\\\"通用设备制造业\\\",\\\"categoryCodeFirst\\\":\\\"C\\\",\\\"categoryCodeSecond\\\":\\\"34\\\",\\\"categoryCodeThird\\\":\\\"346\\\",\\\"categoryMiddle\\\":\\\"烘炉、风机、包装等设备制造\\\",\\\"categorySmall\\\":\\\"\\\"},\\\"isMicroEnt\\\":0,\\\"legalPersonName\\\":\\\"施小友\\\",\\\"name\\\":\\\"浙江百达精工股份有限公司\\\",\\\"orgNumber\\\":\\\"72004563-7\\\",\\\"percentileScore\\\":9267,\\\"phoneNumber\\\":\\\"0576-88488860\\\",\\\"property3\\\":\\\"Zhejiang Baida Precision Manufacturing Corp.\\\",\\\"regCapital\\\":\\\"20217.5636万人民币\\\",\\\"regCapitalCurrency\\\":\\\"人民币\\\",\\\"regInstitute\\\":\\\"浙江省市场监督管理局\\\",\\\"regLocation\\\":\\\"浙江省台州市台州湾新区海城路2399号\\\",\\\"regNumber\\\":\\\"331000000000905\\\",\\\"regStatus\\\":\\\"存续\\\",\\\"scale\\\":\\\"大型\\\",\\\"socialStaffNum\\\":1246,\\\"staffNumRange\\\":\\\"1000-4999人\\\",\\\"tags\\\":\\\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\\\",\\\"taxNumber\\\":\\\"913310007200456372\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"toTime\\\":253392422400000,\\\"type\\\":1,\\\"updateTime\\\":1754033435185,\\\"updateTimes\\\":1752112735000,\\\"usedBondName\\\":\\\"\\\",\\\"websiteList\\\":\\\"http://www.baidapm.com\\\"},{\\\"actualCapital\\\":\\\"185000万人民币\\\",\\\"actualCapitalCurrency\\\":\\\"人民币\\\",\\\"alias\\\":\\\"小米科技\\\",\\\"approvedTime\\\":1732118400000,\\\"base\\\":\\\"bj\\\",\\\"businessScope\\\":\\\"一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口；进出口代理；通讯设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；个人卫生用品销售；卫生用品和一次性使用医疗用品销售；日用杂品销售；日用百货销售；日用品销售；化妆品批发；化妆品零售；第一类医疗器械销售；第二类医疗器械销售；玩具销售；体育用品及器材零售；体育用品及器材批发；文具用品零售；文具用品批发；鞋帽批发；鞋帽零售；服装服饰批发；服装服饰零售；钟表销售；眼镜销售（不含隐形眼镜）；针纺织品销售；家用电器销售；日用家电零售；家具销售；礼品花卉销售；农作物种子经营（仅限不再分装的包装种子）；照相机及器材销售；照相器材及望远镜批发；照相器材及望远镜零售；工艺美术品及收藏品零售（象牙及其制品除外）；工艺美术品及礼仪用品销售（象牙及其制品除外）；计算机软硬件及辅助设备零售；计算机软硬件及辅助设备批发；珠宝首饰零售；珠宝首饰批发；食用农产品批发；食用农产品零售；宠物食品及用品批发；宠物食品及用品零售；电子产品销售；摩托车及零配件零售；摩托车及零配件批发；电动自行车销售；助动自行车、代步车及零配件销售；自行车及零配件零售；自行车及零配件批发；单用途商业预付卡代理销售；商用密码产品销售；五金产品批发；五金产品零售；建筑材料销售；仪器仪表修理；计算机及办公设备维修；办公设备销售；会议及展览服务；组织文化艺术交流活动；广告设计、代理；广告制作；广告发布；摄影扩印服务；票务代理服务；通讯设备修理；移动终端设备制造；可穿戴智能设备制造。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）许可项目：第三类医疗器械经营；网络文化经营；出版物零售；出版物批发；食品销售；药品零售；广播电视节目制作经营；第一类增值电信业务；第二类增值电信业务；在线数据处理与交易处理业务（经营类电子商务）；基础电信业务；互联网信息服务；信息网络传播视听节目。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）（不得从事国家和本市产业政策禁止和限制类项目的经营活动。）\\\",\\\"city\\\":\\\"北京市\\\",\\\"companyId\\\":23402373,\\\"companyOrgType\\\":\\\"有限责任公司(自然人投资或控股)\\\",\\\"createTime\\\":1754035025699,\\\"creditCode\\\":\\\"91110108551385082Q\\\",\\\"district\\\":\\\"海淀区\\\",\\\"districtCode\\\":\\\"110108\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"emailList\\\":[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"],\\\"fromTime\\\":1267545600000,\\\"historyNameList\\\":[\\\"北京小米科技有限责任公司\\\"],\\\"historyNames\\\":\\\"北京小米科技有限责任公司;\\\",\\\"id\\\":\\\"688c7351cc2f9762f84fced7\\\",\\\"industry\\\":\\\"计算机、通信和其他电子设备制造业\\\",\\\"industryAll\\\":{\\\"category\\\":\\\"制造业\\\",\\\"categoryBig\\\":\\\"计算机、通信和其他电子设备制造业\\\",\\\"categoryCodeFirst\\\":\\\"C\\\",\\\"categoryCodeSecond\\\":\\\"39\\\",\\\"categoryCodeThird\\\":\\\"392\\\",\\\"categoryMiddle\\\":\\\"通信设备制造\\\",\\\"categorySmall\\\":\\\"\\\"},\\\"isMicroEnt\\\":1,\\\"legalPersonName\\\":\\\"雷军\\\",\\\"name\\\":\\\"小米科技有限责任公司\\\",\\\"orgNumber\\\":\\\"55138508-2\\\",\\\"percentileScore\\\":9855,\\\"phoneNumber\\\":\\\"010-69630728\\\",\\\"property3\\\":\\\"Beijing Xiaomi Technology Co., Ltd.\\\",\\\"regCapital\\\":\\\"185000万人民币\\\",\\\"regCapitalCurrency\\\":\\\"人民币\\\",\\\"regInstitute\\\":\\\"北京市海淀区市场监督管理局\\\",\\\"regLocation\\\":\\\"北京市海淀区西二旗中路33号院6号楼6层006号\\\",\\\"regNumber\\\":\\\"110108012660422\\\",\\\"regStatus\\\":\\\"存续\\\",\\\"socialStaffNum\\\":37,\\\"staffNumRange\\\":\\\"小于50人\\\",\\\"tags\\\":\\\"存续;曾用名;投资机构;赴港上市(正常上市);小微企业;高新技术企业;独角兽;瞪羚企业;合作风险;竞争风险;司法案件;战略融资\\\",\\\"taxNumber\\\":\\\"91110108551385082Q\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":1,\\\"updateTime\\\":1754035025699,\\\"updateTimes\\\":1753952116000,\\\"websiteList\\\":\\\"https://www.mi.com\\\"}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":1290,\"traceId\":\"493b7a2f4d694425\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:20.013+08:00","traceId":"93280e6a7d14601e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963419964,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":47,\"traceId\":\"93280e6a7d14601e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:20.999+08:00","traceId":"c0ee7792c0220169","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963420246,\"desc\":\"分页查询企业客户统计\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise\",\"module\":\"企业客户管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"agentCode\\\":\\\"\\\",\\\"agentName\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":8,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"agentCode\\\":\\\"640172502\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"沈国强\\\",\\\"emplayeeOpportunityNum\\\":2,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"大童上海法人公司\\\",\\\"opportunityNum\\\":2,\\\"salesCenterName\\\":\\\"大童上海市区扬紫销售团队\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT002\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"李四\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"上海总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"上海销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT003\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"王五\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"广州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"广州销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT004\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"赵六\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"深圳总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"深圳销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT005\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"孙七\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"成都总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"成都销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT006\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"周八\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"武汉总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"武汉销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT007\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"吴九\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"西安总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"西安销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT008\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"郑十\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"杭州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"杭州销售中心\\\",\\\"verifiedEnterpriseNum\\\":1}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":8,\\\"startRow\\\":1,\\\"total\\\":8},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":752,\"traceId\":\"c0ee7792c0220169\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:24.299+08:00","traceId":"9888cad4f87a2f68","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963424246,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":50,\"traceId\":\"9888cad4f87a2f68\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:24.630+08:00","traceId":"19e6b05e7e3256b1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-1","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963424468,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":5,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\">=500000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":500000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000},{\\\"code\\\":\\\"B\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754471503000,\\\"description\\\":\\\"B类公司\\\",\\\"employeeRangeText\\\":\\\">=50000人\\\",\\\"id\\\":\\\"68931c4f739d9025b7e7a662\\\",\\\"name\\\":\\\"B类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754471503000},{\\\"code\\\":\\\"C\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881954000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=5000人\\\",\\\"id\\\":\\\"68995fa257eb6e16dfaf61dc\\\",\\\"name\\\":\\\"C类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":5000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881954000},{\\\"code\\\":\\\"D\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881995000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=500人\\\",\\\"id\\\":\\\"68995fcb57eb6e16dfaf61dd\\\",\\\"name\\\":\\\"D类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":500}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881995000},{\\\"code\\\":\\\"E\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754882012378,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=50人\\\",\\\"id\\\":\\\"68995fdc57eb6e16dfaf61de\\\",\\\"name\\\":\\\"E类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":50}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754882012378}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":5,\\\"startRow\\\":1,\\\"total\\\":5},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":161,\"traceId\":\"19e6b05e7e3256b1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:35.541+08:00","traceId":"372fe42df4b041af","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963435482,\"desc\":\"查询企业类型详情\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/detail\",\"module\":\"企业类型管理\",\"params\":\"{\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\"}\",\"response\":\"{\\\"datas\\\":{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":500000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":58,\"traceId\":\"372fe42df4b041af\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:39.213+08:00","traceId":"e24c4e3d72c4ccb6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963439140,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":68,\"traceId\":\"e24c4e3d72c4ccb6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:39.410+08:00","traceId":"ce624a116b4bd505","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963439330,\"desc\":\"分页查询企业客户统计\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise\",\"module\":\"企业客户管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"agentCode\\\":\\\"\\\",\\\"agentName\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":8,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"agentCode\\\":\\\"640172502\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"沈国强\\\",\\\"emplayeeOpportunityNum\\\":2,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"大童上海法人公司\\\",\\\"opportunityNum\\\":2,\\\"salesCenterName\\\":\\\"大童上海市区扬紫销售团队\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT002\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"李四\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"上海总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"上海销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT003\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"王五\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"广州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"广州销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT004\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"赵六\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"深圳总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"深圳销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT005\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"孙七\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"成都总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"成都销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT006\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"周八\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"武汉总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"武汉销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT007\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"吴九\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"西安总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"西安销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT008\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"郑十\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"杭州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"杭州销售中心\\\",\\\"verifiedEnterpriseNum\\\":1}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":8,\\\"startRow\\\":1,\\\"total\\\":8},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":79,\"traceId\":\"ce624a116b4bd505\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:40.941+08:00","traceId":"c6d0f4c39c8fde03","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-6","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963440733,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":201,\"traceId\":\"c6d0f4c39c8fde03\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:41.064+08:00","traceId":"d07b97f822a1b24e","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963440980,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":5,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\">=500000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":500000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000},{\\\"code\\\":\\\"B\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754471503000,\\\"description\\\":\\\"B类公司\\\",\\\"employeeRangeText\\\":\\\">=50000人\\\",\\\"id\\\":\\\"68931c4f739d9025b7e7a662\\\",\\\"name\\\":\\\"B类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754471503000},{\\\"code\\\":\\\"C\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881954000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=5000人\\\",\\\"id\\\":\\\"68995fa257eb6e16dfaf61dc\\\",\\\"name\\\":\\\"C类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":5000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881954000},{\\\"code\\\":\\\"D\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881995000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=500人\\\",\\\"id\\\":\\\"68995fcb57eb6e16dfaf61dd\\\",\\\"name\\\":\\\"D类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":500}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881995000},{\\\"code\\\":\\\"E\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754882012378,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=50人\\\",\\\"id\\\":\\\"68995fdc57eb6e16dfaf61de\\\",\\\"name\\\":\\\"E类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":50}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754882012378}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":5,\\\"startRow\\\":1,\\\"total\\\":5},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":83,\"traceId\":\"d07b97f822a1b24e\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:43.398+08:00","traceId":"d980c8256c3cf729","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963443358,\"desc\":\"查询企业类型详情\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/detail\",\"module\":\"企业类型管理\",\"params\":\"{\\\"id\\\":\\\"68931c4f739d9025b7e7a662\\\"}\",\"response\":\"{\\\"datas\\\":{\\\"code\\\":\\\"B\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754471503000,\\\"description\\\":\\\"B类公司\\\",\\\"id\\\":\\\"68931c4f739d9025b7e7a662\\\",\\\"name\\\":\\\"B类\\\",\\\"priority\\\":1,\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754471503000},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":39,\"traceId\":\"d980c8256c3cf729\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:46.605+08:00","traceId":"6ef9683aeeb6bf0d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-9","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963446526,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":5,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\">=500000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":500000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000},{\\\"code\\\":\\\"B\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754471503000,\\\"description\\\":\\\"B类公司\\\",\\\"employeeRangeText\\\":\\\">=50000人\\\",\\\"id\\\":\\\"68931c4f739d9025b7e7a662\\\",\\\"name\\\":\\\"B类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754471503000},{\\\"code\\\":\\\"C\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881954000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=5000人\\\",\\\"id\\\":\\\"68995fa257eb6e16dfaf61dc\\\",\\\"name\\\":\\\"C类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":5000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881954000},{\\\"code\\\":\\\"D\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881995000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=500人\\\",\\\"id\\\":\\\"68995fcb57eb6e16dfaf61dd\\\",\\\"name\\\":\\\"D类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":500}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881995000},{\\\"code\\\":\\\"E\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754882012378,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=50人\\\",\\\"id\\\":\\\"68995fdc57eb6e16dfaf61de\\\",\\\"name\\\":\\\"E类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":50}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754882012378}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":5,\\\"startRow\\\":1,\\\"total\\\":5},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":79,\"traceId\":\"6ef9683aeeb6bf0d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:49.087+08:00","traceId":"d9b09eb0d46f9bf4","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963449037,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":49,\"traceId\":\"d9b09eb0d46f9bf4\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:50:49.179+08:00","traceId":"994227a21d9c1ef4","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963449097,\"desc\":\"分页查询企业客户统计\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise\",\"module\":\"企业客户管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"agentCode\\\":\\\"\\\",\\\"agentName\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":8,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"agentCode\\\":\\\"640172502\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"沈国强\\\",\\\"emplayeeOpportunityNum\\\":2,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"大童上海法人公司\\\",\\\"opportunityNum\\\":2,\\\"salesCenterName\\\":\\\"大童上海市区扬紫销售团队\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT002\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"李四\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"上海总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"上海销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT003\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"王五\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"广州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"广州销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT004\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"赵六\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"深圳总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"深圳销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT005\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"孙七\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"成都总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"成都销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT006\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"周八\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"武汉总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"武汉销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT007\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"吴九\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"西安总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"西安销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT008\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"郑十\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"杭州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"杭州销售中心\\\",\\\"verifiedEnterpriseNum\\\":1}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":8,\\\"startRow\\\":1,\\\"total\\\":8},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":81,\"traceId\":\"994227a21d9c1ef4\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:54:52.066+08:00","traceId":"64402118dcbaeef1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-8","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963691979,\"desc\":\"分页查询企业类型列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page\",\"module\":\"企业类型管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":5,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"code\\\":\\\"A\\\",\\\"createId\\\":\\\"U000162\\\",\\\"createTime\\\":1753668834000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\">=500000人\\\",\\\"id\\\":\\\"6886dce2abe72c15d2062839\\\",\\\"name\\\":\\\"A类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\">=600000万元\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":500000},{\\\"field\\\":\\\"revenue\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":6000000000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000162\\\",\\\"updateTime\\\":1753668834000},{\\\"code\\\":\\\"B\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754471503000,\\\"description\\\":\\\"B类公司\\\",\\\"employeeRangeText\\\":\\\">=50000人\\\",\\\"id\\\":\\\"68931c4f739d9025b7e7a662\\\",\\\"name\\\":\\\"B类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\">=\\\",\\\"value\\\":50000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754471503000},{\\\"code\\\":\\\"C\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881954000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=5000人\\\",\\\"id\\\":\\\"68995fa257eb6e16dfaf61dc\\\",\\\"name\\\":\\\"C类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":5000}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881954000},{\\\"code\\\":\\\"D\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754881995000,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=500人\\\",\\\"id\\\":\\\"68995fcb57eb6e16dfaf61dd\\\",\\\"name\\\":\\\"D类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":500}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754881995000},{\\\"code\\\":\\\"E\\\",\\\"createId\\\":\\\"U000211\\\",\\\"createTime\\\":1754882012378,\\\"description\\\":\\\"\\\",\\\"employeeRangeText\\\":\\\"<=50人\\\",\\\"id\\\":\\\"68995fdc57eb6e16dfaf61de\\\",\\\"name\\\":\\\"E类\\\",\\\"priority\\\":1,\\\"revenueRangeText\\\":\\\"-\\\",\\\"rules\\\":[{\\\"field\\\":\\\"employeeCount\\\",\\\"operator\\\":\\\"<=\\\",\\\"value\\\":50}],\\\"tenantId\\\":\\\"T0001\\\",\\\"updateId\\\":\\\"U000211\\\",\\\"updateTime\\\":1754882012378}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":5,\\\"startRow\\\":1,\\\"total\\\":5},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":86,\"traceId\":\"64402118dcbaeef1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:54:52.067+08:00","traceId":"2dc001181650a09d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-5","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963691766,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":300,\"traceId\":\"2dc001181650a09d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:54:52.757+08:00","traceId":"212da2b226dcfb89","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-7","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963692703,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":53,\"traceId\":\"212da2b226dcfb89\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:54:53.130+08:00","traceId":"9ce05d24f43378b6","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-10","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963693048,\"desc\":\"分页查询企业客户统计\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise\",\"module\":\"企业客户管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{\\\"agentCode\\\":\\\"\\\",\\\"agentName\\\":\\\"\\\"}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":8,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"agentCode\\\":\\\"640172502\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"沈国强\\\",\\\"emplayeeOpportunityNum\\\":2,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"大童上海法人公司\\\",\\\"opportunityNum\\\":2,\\\"salesCenterName\\\":\\\"大童上海市区扬紫销售团队\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT002\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"李四\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"上海总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"上海销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT003\\\",\\\"agentEnterpriseNum\\\":0,\\\"agentName\\\":\\\"王五\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"广州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"广州销售中心\\\",\\\"verifiedEnterpriseNum\\\":0},{\\\"agentCode\\\":\\\"AGT004\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"赵六\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"深圳总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"深圳销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT005\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"孙七\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"成都总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"成都销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT006\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"周八\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"武汉总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"武汉销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT007\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"吴九\\\",\\\"emplayeeOpportunityNum\\\":1,\\\"generalOpportunityNum\\\":0,\\\"legalName\\\":\\\"西安总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"西安销售中心\\\",\\\"verifiedEnterpriseNum\\\":1},{\\\"agentCode\\\":\\\"AGT008\\\",\\\"agentEnterpriseNum\\\":1,\\\"agentName\\\":\\\"郑十\\\",\\\"emplayeeOpportunityNum\\\":0,\\\"generalOpportunityNum\\\":1,\\\"legalName\\\":\\\"杭州总公司\\\",\\\"opportunityNum\\\":1,\\\"salesCenterName\\\":\\\"杭州销售中心\\\",\\\"verifiedEnterpriseNum\\\":1}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":8,\\\"startRow\\\":1,\\\"total\\\":8},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":81,\"traceId\":\"9ce05d24f43378b6\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:58:28.123+08:00","traceId":"5e90c4cc92a4ea4d","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-3","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963908010,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":113,\"traceId\":\"5e90c4cc92a4ea4d\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:58:29.679+08:00","traceId":"fca86c6d48dd7562","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-4","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963909637,\"desc\":\"查询当前用户角色权限\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth\",\"module\":\"用户管理\",\"params\":\"{}\",\"response\":\"{\\\"datas\\\":{\\\"roleAuthStr\\\":\\\"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download\\\",\\\"userId\\\":\\\"U000162\\\",\\\"userName\\\":\\\"甘杰\\\",\\\"userRoles\\\":[{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:receive\\\",\\\"authName\\\":\\\"领取机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":1,\\\"roleName\\\":\\\"分公司统筹角色\\\",\\\"roleType\\\":\\\"1\\\"},{\\\"roleAuths\\\":[{\\\"authCode\\\":\\\"elms:opportunity:stop\\\",\\\"authName\\\":\\\"暂停机会\\\"},{\\\"authCode\\\":\\\"elms:opportunity:close:nullity\\\",\\\"authName\\\":\\\"无效\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:pause\\\",\\\"authName\\\":\\\"暂停重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:restart:fail\\\",\\\"authName\\\":\\\"推进失败重启\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:basic\\\",\\\"authName\\\":\\\"基础信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:customer\\\",\\\"authName\\\":\\\"客户信息\\\"},{\\\"authCode\\\":\\\"elms:opportunity:details:progress\\\",\\\"authName\\\":\\\"机会进度\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:edit\\\",\\\"authName\\\":\\\"编辑\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:log:add\\\",\\\"authName\\\":\\\"添加\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:team:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:member:divide:view:total\\\",\\\"authName\\\":\\\"可见全部人员\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:upload\\\",\\\"authName\\\":\\\"上传\\\"},{\\\"authCode\\\":\\\"elms:opportunity:summary:delete\\\",\\\"authName\\\":\\\"删除\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:ecology:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:bidding:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:authorization:download\\\",\\\"authName\\\":\\\"下载\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:view\\\",\\\"authName\\\":\\\"查看\\\"},{\\\"authCode\\\":\\\"elms:opportunity:data:result:download\\\",\\\"authName\\\":\\\"下载\\\"}],\\\"roleId\\\":15,\\\"roleName\\\":\\\"总公司统筹角色\\\",\\\"roleType\\\":\\\"2\\\"}]},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"查询成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":42,\"traceId\":\"fca86c6d48dd7562\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
{"@timestamp":"2025-08-12T09:58:30.366+08:00","traceId":"c8827571826480e1","remoteIp":"127.0.0.1","level":"INFO","xB3TraceId":"","xB3SpanId":"","xB3ParentId":"","thread":"http-nio-7003-exec-2","class":"c.k.kbcbsc.log.util.OperateLogELKHelper","msg":"{\"action\":\"查询\",\"appName\":\"kbc-elms-web\",\"createTime\":1754963910286,\"desc\":\"分页查询企业基本信息列表\",\"flag\":true,\"ip\":\"127.0.0.1\",\"method\":\"com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page\",\"module\":\"企业信息管理\",\"params\":\"{\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"param\\\":{}}\",\"response\":\"{\\\"datas\\\":{\\\"endRow\\\":2,\\\"hasNextPage\\\":false,\\\"hasPreviousPage\\\":false,\\\"isFirstPage\\\":true,\\\"isLastPage\\\":true,\\\"list\\\":[{\\\"actualCapital\\\":\\\"20217.5636万人民币\\\",\\\"actualCapitalCurrency\\\":\\\"人民币\\\",\\\"alias\\\":\\\"百达精工\\\",\\\"approvedTime\\\":1749744000000,\\\"base\\\":\\\"zj\\\",\\\"bondName\\\":\\\"百达精工\\\",\\\"bondNum\\\":\\\"603331\\\",\\\"bondType\\\":\\\"A股\\\",\\\"businessScope\\\":\\\"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。\\\",\\\"city\\\":\\\"台州市\\\",\\\"companyId\\\":44302802,\\\"companyOrgType\\\":\\\"其他股份有限公司(上市)\\\",\\\"createTime\\\":1754011649790,\\\"creditCode\\\":\\\"913310007200456372\\\",\\\"district\\\":\\\"椒江区\\\",\\\"districtCode\\\":\\\"331002\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"emailList\\\":[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"],\\\"fromTime\\\":965577600000,\\\"historyNameList\\\":[\\\"台州市百达制冷有限公司\\\"],\\\"historyNames\\\":\\\"台州市百达制冷有限公司;\\\",\\\"id\\\":\\\"688c1802bf13d11683ba16ba\\\",\\\"industry\\\":\\\"通用设备制造业\\\",\\\"industryAll\\\":{\\\"category\\\":\\\"制造业\\\",\\\"categoryBig\\\":\\\"通用设备制造业\\\",\\\"categoryCodeFirst\\\":\\\"C\\\",\\\"categoryCodeSecond\\\":\\\"34\\\",\\\"categoryCodeThird\\\":\\\"346\\\",\\\"categoryMiddle\\\":\\\"烘炉、风机、包装等设备制造\\\",\\\"categorySmall\\\":\\\"\\\"},\\\"isMicroEnt\\\":0,\\\"legalPersonName\\\":\\\"施小友\\\",\\\"name\\\":\\\"浙江百达精工股份有限公司\\\",\\\"orgNumber\\\":\\\"72004563-7\\\",\\\"percentileScore\\\":9267,\\\"phoneNumber\\\":\\\"0576-88488860\\\",\\\"property3\\\":\\\"Zhejiang Baida Precision Manufacturing Corp.\\\",\\\"regCapital\\\":\\\"20217.5636万人民币\\\",\\\"regCapitalCurrency\\\":\\\"人民币\\\",\\\"regInstitute\\\":\\\"浙江省市场监督管理局\\\",\\\"regLocation\\\":\\\"浙江省台州市台州湾新区海城路2399号\\\",\\\"regNumber\\\":\\\"331000000000905\\\",\\\"regStatus\\\":\\\"存续\\\",\\\"scale\\\":\\\"大型\\\",\\\"socialStaffNum\\\":1246,\\\"staffNumRange\\\":\\\"1000-4999人\\\",\\\"tags\\\":\\\"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件\\\",\\\"taxNumber\\\":\\\"913310007200456372\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"toTime\\\":253392422400000,\\\"type\\\":1,\\\"updateTime\\\":1754033435185,\\\"updateTimes\\\":1752112735000,\\\"usedBondName\\\":\\\"\\\",\\\"websiteList\\\":\\\"http://www.baidapm.com\\\"},{\\\"actualCapital\\\":\\\"185000万人民币\\\",\\\"actualCapitalCurrency\\\":\\\"人民币\\\",\\\"alias\\\":\\\"小米科技\\\",\\\"approvedTime\\\":1732118400000,\\\"base\\\":\\\"bj\\\",\\\"businessScope\\\":\\\"一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口；进出口代理；通讯设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；个人卫生用品销售；卫生用品和一次性使用医疗用品销售；日用杂品销售；日用百货销售；日用品销售；化妆品批发；化妆品零售；第一类医疗器械销售；第二类医疗器械销售；玩具销售；体育用品及器材零售；体育用品及器材批发；文具用品零售；文具用品批发；鞋帽批发；鞋帽零售；服装服饰批发；服装服饰零售；钟表销售；眼镜销售（不含隐形眼镜）；针纺织品销售；家用电器销售；日用家电零售；家具销售；礼品花卉销售；农作物种子经营（仅限不再分装的包装种子）；照相机及器材销售；照相器材及望远镜批发；照相器材及望远镜零售；工艺美术品及收藏品零售（象牙及其制品除外）；工艺美术品及礼仪用品销售（象牙及其制品除外）；计算机软硬件及辅助设备零售；计算机软硬件及辅助设备批发；珠宝首饰零售；珠宝首饰批发；食用农产品批发；食用农产品零售；宠物食品及用品批发；宠物食品及用品零售；电子产品销售；摩托车及零配件零售；摩托车及零配件批发；电动自行车销售；助动自行车、代步车及零配件销售；自行车及零配件零售；自行车及零配件批发；单用途商业预付卡代理销售；商用密码产品销售；五金产品批发；五金产品零售；建筑材料销售；仪器仪表修理；计算机及办公设备维修；办公设备销售；会议及展览服务；组织文化艺术交流活动；广告设计、代理；广告制作；广告发布；摄影扩印服务；票务代理服务；通讯设备修理；移动终端设备制造；可穿戴智能设备制造。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）许可项目：第三类医疗器械经营；网络文化经营；出版物零售；出版物批发；食品销售；药品零售；广播电视节目制作经营；第一类增值电信业务；第二类增值电信业务；在线数据处理与交易处理业务（经营类电子商务）；基础电信业务；互联网信息服务；信息网络传播视听节目。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）（不得从事国家和本市产业政策禁止和限制类项目的经营活动。）\\\",\\\"city\\\":\\\"北京市\\\",\\\"companyId\\\":23402373,\\\"companyOrgType\\\":\\\"有限责任公司(自然人投资或控股)\\\",\\\"createTime\\\":1754035025699,\\\"creditCode\\\":\\\"91110108551385082Q\\\",\\\"district\\\":\\\"海淀区\\\",\\\"districtCode\\\":\\\"110108\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"emailList\\\":[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"],\\\"fromTime\\\":1267545600000,\\\"historyNameList\\\":[\\\"北京小米科技有限责任公司\\\"],\\\"historyNames\\\":\\\"北京小米科技有限责任公司;\\\",\\\"id\\\":\\\"688c7351cc2f9762f84fced7\\\",\\\"industry\\\":\\\"计算机、通信和其他电子设备制造业\\\",\\\"industryAll\\\":{\\\"category\\\":\\\"制造业\\\",\\\"categoryBig\\\":\\\"计算机、通信和其他电子设备制造业\\\",\\\"categoryCodeFirst\\\":\\\"C\\\",\\\"categoryCodeSecond\\\":\\\"39\\\",\\\"categoryCodeThird\\\":\\\"392\\\",\\\"categoryMiddle\\\":\\\"通信设备制造\\\",\\\"categorySmall\\\":\\\"\\\"},\\\"isMicroEnt\\\":1,\\\"legalPersonName\\\":\\\"雷军\\\",\\\"name\\\":\\\"小米科技有限责任公司\\\",\\\"orgNumber\\\":\\\"55138508-2\\\",\\\"percentileScore\\\":9855,\\\"phoneNumber\\\":\\\"010-69630728\\\",\\\"property3\\\":\\\"Beijing Xiaomi Technology Co., Ltd.\\\",\\\"regCapital\\\":\\\"185000万人民币\\\",\\\"regCapitalCurrency\\\":\\\"人民币\\\",\\\"regInstitute\\\":\\\"北京市海淀区市场监督管理局\\\",\\\"regLocation\\\":\\\"北京市海淀区西二旗中路33号院6号楼6层006号\\\",\\\"regNumber\\\":\\\"110108012660422\\\",\\\"regStatus\\\":\\\"存续\\\",\\\"socialStaffNum\\\":37,\\\"staffNumRange\\\":\\\"小于50人\\\",\\\"tags\\\":\\\"存续;曾用名;投资机构;赴港上市(正常上市);小微企业;高新技术企业;独角兽;瞪羚企业;合作风险;竞争风险;司法案件;战略融资\\\",\\\"taxNumber\\\":\\\"91110108551385082Q\\\",\\\"tenantId\\\":\\\"T0001\\\",\\\"type\\\":1,\\\"updateTime\\\":1754035025699,\\\"updateTimes\\\":1753952116000,\\\"websiteList\\\":\\\"https://www.mi.com\\\"}],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":1,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1],\\\"nextPage\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"pages\\\":1,\\\"prePage\\\":0,\\\"size\\\":2,\\\"startRow\\\":1,\\\"total\\\":2},\\\"resp_code\\\":0,\\\"resp_msg\\\":\\\"请求成功\\\"}\",\"serviceType\":\"bs\",\"tenantId\":\"T0001\",\"time\":79,\"traceId\":\"c8827571826480e1\",\"userId\":\"U000162\",\"userName\":\"甘杰\"}"}
