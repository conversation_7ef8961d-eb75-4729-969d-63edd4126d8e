[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:57.312 DEBUG 19636 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:59.058 DEBUG 19636 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:59.158 DEBUG 19636 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.497 DEBUG 19636 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.582 DEBUG 19636 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.158 DEBUG 19636 [main] com.kbao.kbcelms.formula.service.FormulaCalculationService interface com.kbao.kbcelms.formula.dao.FormulaCalculationLogMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.177 DEBUG 19636 [main] com.kbao.kbcelms.formula.service.FormulaService interface com.kbao.kbcelms.formula.dao.FormulaMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.217 DEBUG 19636 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.349 DEBUG 19636 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.368 DEBUG 19636 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.390 DEBUG 19636 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.413 DEBUG 19636 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.434 DEBUG 19636 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.457 DEBUG 19636 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.741 DEBUG 19636 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.825 DEBUG 19636 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.945 DEBUG 19636 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.400 DEBUG 19636 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.427 DEBUG 19636 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.457 DEBUG 19636 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.479 DEBUG 19636 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.514 DEBUG 19636 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.581 DEBUG 19636 [main] com.kbao.kbcelms.industrylimit.service.IndustryLimitService interface com.kbao.kbcelms.industrylimit.dao.IndustryLimitMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.814 DEBUG 19636 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.965 DEBUG 19636 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireQuestionOptionService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionOptionMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.988 DEBUG 19636 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireQuestionService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.009 DEBUG 19636 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.041 DEBUG 19636 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireAnswerService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireAnswerMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.100 DEBUG 19636 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixLevelService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixLevelMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.126 DEBUG 19636 [main] com.kbao.kbcelms.riskMatrix.service.CategoryScoreItemService interface com.kbao.kbcelms.riskMatrix.dao.CategoryScoreItemMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.149 DEBUG 19636 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixCategoryService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixCategoryMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.183 DEBUG 19636 [main] com.kbao.kbcelms.riskMatrix.service.ScoreItemCriteriaService interface com.kbao.kbcelms.riskMatrix.dao.ScoreItemCriteriaMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.206 DEBUG 19636 [main] com.kbao.kbcelms.riskMatrix.service.ScoreItemService interface com.kbao.kbcelms.riskMatrix.dao.ScoreItemMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.231 DEBUG 19636 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.589 DEBUG 19636 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixResultService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixResultMapper
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.540 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.540 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.542 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 73e47d749a3a8044
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.542 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 40d0b1b478e84c97
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.542 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.542 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.543 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.544 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.544 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (409ms)
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (409ms)
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:29 GMT
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:29 GMT
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.953 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.954 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.954 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.954 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.954 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.955 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.955 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:28.955 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:28.955 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.259 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.259 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 73e47d749a3a8044
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.260 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.260 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.260 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.260 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.260 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.261 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.345 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (84ms)
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.346 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.346 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.346 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:47:29 GMT
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.347 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.347 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.347 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.347 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.347 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [73e47d749a3a8044,] 2025-08-12 09:47:29.347 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:29.698 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:29.955 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:30.322 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.690 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.694 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.694 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e73514d2558fe615
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.694 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d6c0fb44b59ad5bb
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.694 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.695 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.695 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.696 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.696 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.696 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.696 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.697 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.697 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.697 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.697 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.697 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.819 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (122ms)
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.819 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.820 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.820 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:35 GMT
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.820 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.821 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.821 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.821 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.821 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (124ms)
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.821 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.821 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.822 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.822 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.822 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:35 GMT
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.822 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.822 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.822 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.822 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.825 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.825 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.872 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.873 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.873 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: e73514d2558fe615
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.873 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.875 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.875 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.875 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.874 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.875 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.875 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.932 DEBUG 19636 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.959 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.960 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.960 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.960 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:47:35 GMT
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.961 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.961 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.961 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.961 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.961 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [e73514d2558fe615,] 2025-08-12 09:47:34.961 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.540 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.540 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.540 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5c0ba31539d8e252
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.540 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 0c4731b624e3b6c1
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.540 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.540 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.540 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.541 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.541 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.541 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.541 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.542 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.542 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.542 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.542 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.542 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.640 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (96ms)
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.640 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.640 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.640 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:35 GMT
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.640 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.640 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.640 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.642 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.642 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.642 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:35 GMT
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.643 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.646 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.647 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.690 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.690 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.699 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.700 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 5c0ba31539d8e252
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.700 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.700 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.700 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.700 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.700 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.701 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.730 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.783 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.783 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.783 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.784 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:47:35 GMT
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.784 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.784 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.784 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.784 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.785 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [5c0ba31539d8e252,] 2025-08-12 09:47:35.785 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.920 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.920 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 4c7f1927eadda760
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.920 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.920 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.921 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.921 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.921 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:35.921 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.008 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (87ms)
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.009 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.009 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.009 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:36 GMT
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.010 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.010 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.010 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.010 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.010 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.010 DEBUG 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.530 DEBUG 19636 [http-nio-7003-exec-7] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.763 DEBUG 19636 [http-nio-7003-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.239 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.243 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 8fb36825e37fc054
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.236 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.244 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 1891ce97e30ae8ec
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.246 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.246 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.244 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.252 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.253 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.365 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (106ms)
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.365 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.365 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.365 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:44 GMT
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.365 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.365 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.366 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.367 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.367 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.367 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.372 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (117ms)
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.372 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.372 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.373 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:44 GMT
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.373 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.374 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.374 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.375 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.375 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.376 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.426 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.429 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 1891ce97e30ae8ec
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.437 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.479 DEBUG 19636 [http-nio-7003-exec-9] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.505 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (67ms)
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.505 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.505 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.505 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:47:44 GMT
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.505 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.505 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.506 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.506 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.506 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [1891ce97e30ae8ec,] 2025-08-12 09:47:43.506 DEBUG 19636 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.556 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.556 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 54792ee6394ae4de
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.556 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.556 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.556 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.556 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.557 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.557 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.643 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (86ms)
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.644 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.644 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.645 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:44 GMT
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.645 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.645 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.645 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.645 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.645 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.646 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:44.360 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==>  Preparing: select count(0) from (select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code) tmp_count 
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:44.361 DEBUG 19636 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==> Parameters: T0001(String)
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.887 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.887 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.889 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 81dff1a0412538a9
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.889 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 66d65ae441505f49
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.889 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.889 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.890 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.892 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.894 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.894 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.895 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.894 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.896 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.896 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.896 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.896 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.992 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (95ms)
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.993 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.993 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.994 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:47 GMT
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.994 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.994 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.994 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.994 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.994 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (97ms)
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.994 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:47 GMT
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.995 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:46.999 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.000 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:47.072 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:47.073 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 66d65ae441505f49
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.076 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:47.115 DEBUG 19636 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.158 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (80ms)
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.158 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.159 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.159 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:47:47 GMT
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.159 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.159 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.160 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.160 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.160 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [66d65ae441505f49,] 2025-08-12 09:47:47.160 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.171 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.171 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: f2eaaf4bc2bf4d8d
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.171 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.172 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.172 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.172 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.172 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.172 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.243 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (72ms)
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:47 GMT
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.244 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.245 DEBUG 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.329 DEBUG 19636 [http-nio-7003-exec-1] org.springframework.data.mongodb.core.MongoTemplate Executing count: {} in collection: enterprise_type
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.367 DEBUG 19636 [http-nio-7003-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: {} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.type.model.EnterpriseType in collection: enterprise_type
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.780 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.780 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.781 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: cc99d62a6a402a14
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.781 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c520747be448d770
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.781 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.782 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.876 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.876 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.876 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.877 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:51 GMT
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.877 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.877 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.877 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.877 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (95ms)
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:51 GMT
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.878 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.880 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.881 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.925 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.960 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: cc99d62a6a402a14
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.960 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.961 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.961 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.961 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.961 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:50.961 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.927 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.962 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.993 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.993 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 91d1ed891d0f0fa8
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.993 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.994 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.994 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.994 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.994 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:50.994 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:51.002 DEBUG 19636 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.032 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (71ms)
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.032 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.032 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.032 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:47:51 GMT
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.032 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.032 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.033 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.033 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.033 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [cc99d62a6a402a14,] 2025-08-12 09:47:51.033 DEBUG 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.066 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (72ms)
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:47:51 GMT
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.067 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.068 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.121 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==>  Preparing: select count(0) from (select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.isVerified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code) tmp_count 
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.122 DEBUG 19636 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==> Parameters: T0001(String)
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:28.158 DEBUG 20568 [main] com.kbao.commons.filter.TraceContextFilter Filter 'traceContextFilter' configured for use
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:29.658 DEBUG 20568 [main] com.kbao.kbcelms.auth.service.AuthService interface com.kbao.kbcelms.auth.dao.AuthMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:29.717 DEBUG 20568 [main] com.kbao.kbcelms.bascode.service.BasCodeService interface com.kbao.kbcelms.bascode.dao.BasCodeMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.932 DEBUG 20568 [main] com.kbao.kbcelms.constant.service.ConstantConfigService interface com.kbao.kbcelms.constant.dao.ConstantConfigMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.022 DEBUG 20568 [main] com.kbao.kbcelms.dataTemplate.service.DataTemplateService interface com.kbao.kbcelms.dataTemplate.dao.DataTemplateMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.496 DEBUG 20568 [main] com.kbao.kbcelms.formula.service.FormulaCalculationService interface com.kbao.kbcelms.formula.dao.FormulaCalculationLogMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.513 DEBUG 20568 [main] com.kbao.kbcelms.formula.service.FormulaService interface com.kbao.kbcelms.formula.dao.FormulaMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.553 DEBUG 20568 [main] com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService interface com.kbao.kbcelms.genAgentEnterprise.dao.GenAgentEnterpriseMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.685 DEBUG 20568 [main] com.kbao.kbcelms.roleauth.service.RoleAuthService interface com.kbao.kbcelms.roleauth.dao.RoleAuthMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.705 DEBUG 20568 [main] com.kbao.kbcelms.role.service.RoleService interface com.kbao.kbcelms.role.dao.RoleMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.730 DEBUG 20568 [main] com.kbao.kbcelms.usertenant.service.UserTenantService interface com.kbao.kbcelms.usertenant.dao.UserTenantMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.753 DEBUG 20568 [main] com.kbao.kbcelms.opportunityprocesslog.service.OpportunityProcessLogService interface com.kbao.kbcelms.opportunityprocesslog.dao.OpportunityProcessLogMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.778 DEBUG 20568 [main] com.kbao.kbcelms.userrole.service.UserRoleService interface com.kbao.kbcelms.userrole.dao.UserRoleMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.801 DEBUG 20568 [main] com.kbao.kbcelms.userorg.service.UserOrgService interface com.kbao.kbcelms.userorg.dao.UserOrgMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:32.367 DEBUG 20568 [main] com.kbao.kbcelms.user.service.UserService interface com.kbao.kbcelms.user.dao.UserMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:32.466 DEBUG 20568 [main] com.kbao.kbcelms.processdefine.service.ProcessDefineService interface com.kbao.kbcelms.processdefine.dao.ProcessDefineMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:32.669 DEBUG 20568 [main] com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService interface com.kbao.kbcelms.opportunityprocess.dao.OpportunityProcessMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.168 DEBUG 20568 [main] com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService interface com.kbao.kbcelms.opportunityteamdivision.dao.OpportunityTeamDivisionMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.199 DEBUG 20568 [main] com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService interface com.kbao.kbcelms.opportunityteam.dao.OpportunityTeamMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.230 DEBUG 20568 [main] com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService interface com.kbao.kbcelms.opportunitydetail.dao.OpportunityDetailMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.250 DEBUG 20568 [main] com.kbao.kbcelms.opportunity.service.OpportunityService interface com.kbao.kbcelms.opportunity.dao.OpportunityMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.283 DEBUG 20568 [main] com.kbao.kbcelms.industry.service.IndustryService interface com.kbao.kbcelms.industry.dao.IndustryMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.353 DEBUG 20568 [main] com.kbao.kbcelms.industrylimit.service.IndustryLimitService interface com.kbao.kbcelms.industrylimit.dao.IndustryLimitMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.571 DEBUG 20568 [main] com.kbao.kbcelms.opportunityorder.service.OpportunityOrderService interface com.kbao.kbcelms.opportunityorder.dao.OpportunityOrderMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.724 DEBUG 20568 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireQuestionOptionService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionOptionMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.746 DEBUG 20568 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireQuestionService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireQuestionMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.767 DEBUG 20568 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.797 DEBUG 20568 [main] com.kbao.kbcelms.questionnaire.service.QuestionnaireAnswerService interface com.kbao.kbcelms.questionnaire.dao.QuestionnaireAnswerMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.860 DEBUG 20568 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixLevelService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixLevelMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.885 DEBUG 20568 [main] com.kbao.kbcelms.riskMatrix.service.CategoryScoreItemService interface com.kbao.kbcelms.riskMatrix.dao.CategoryScoreItemMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.907 DEBUG 20568 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixCategoryService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixCategoryMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.941 DEBUG 20568 [main] com.kbao.kbcelms.riskMatrix.service.ScoreItemCriteriaService interface com.kbao.kbcelms.riskMatrix.dao.ScoreItemCriteriaMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.963 DEBUG 20568 [main] com.kbao.kbcelms.riskMatrix.service.ScoreItemService interface com.kbao.kbcelms.riskMatrix.dao.ScoreItemMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.986 DEBUG 20568 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixMapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.306 DEBUG 20568 [main] com.kbao.kbcelms.riskMatrix.service.RiskMatrixResultService interface com.kbao.kbcelms.riskMatrix.dao.RiskMatrixResultMapper
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.538 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.538 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.538 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.538 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.538 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 05a8bdc043deae76
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 7f271ea68e3b6ddb
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c09451d24e972b7d
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 493b7a2f4d694425
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d2c3d4e58cc76b5c
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.540 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:56.541 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.542 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.542 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:56.542 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.542 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.542 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:56.542 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:56.542 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.460 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (917ms)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.460 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (917ms)
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.460 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (918ms)
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.460 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (917ms)
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.460 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (917ms)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.461 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.461 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.461 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.461 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.461 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.461 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.461 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:49:58 GMT
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:49:58 GMT
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:49:58 GMT
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:49:58 GMT
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:49:58 GMT
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.462 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.463 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.464 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.465 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:57.465 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:57.465 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.465 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:57.465 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:57.465 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.268 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.268 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.271 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: d2c3d4e58cc76b5c
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.272 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.271 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 05a8bdc043deae76
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.272 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.273 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.273 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.273 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.273 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.274 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.274 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.274 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.274 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.274 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.274 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.389 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (114ms)
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.389 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.389 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.389 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (114ms)
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.389 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:49:59 GMT
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.389 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.389 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:49:59 GMT
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.390 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [05a8bdc043deae76,] 2025-08-12 09:49:58.391 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.391 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [d2c3d4e58cc76b5c,] 2025-08-12 09:49:58.391 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:58.808 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:58.808 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:59.075 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:59.075 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:59.262 DEBUG 20568 [http-nio-7003-exec-5] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:59.349 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:59.349 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:59.461 DEBUG 20568 [http-nio-7003-exec-5] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.803 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.806 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.806 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 98da5e606e6203b1
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.808 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.806 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 93280e6a7d14601e
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.809 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.809 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.809 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.810 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.810 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.810 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.810 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.810 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.814 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.814 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.814 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.918 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (103ms)
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.918 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.918 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.918 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:20 GMT
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.919 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.919 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (104ms)
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.919 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.919 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.919 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.919 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.919 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.920 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:20 GMT
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.920 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.920 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.920 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.920 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.920 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.920 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.927 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.928 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.965 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.966 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.975 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.975 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 98da5e606e6203b1
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.976 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.976 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.976 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.976 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.976 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:19.976 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:20.010 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.060 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.060 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.060 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.060 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:50:20 GMT
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.060 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.061 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.061 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.061 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.062 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [98da5e606e6203b1,] 2025-08-12 09:50:20.062 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.111 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.111 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c0ee7792c0220169
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.111 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.111 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.111 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.112 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.112 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.112 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.191 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (79ms)
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.193 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.193 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.193 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:20 GMT
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.193 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.194 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.194 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.194 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.194 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.194 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.900 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==>  Preparing: select count(0) from (select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code) tmp_count 
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.901 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==> Parameters: T0001(String)
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.954 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT <==      Total: 1
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.956 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==>  Preparing: select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code LIMIT ? 
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.956 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==> Parameters: T0001(String), 10(Integer)
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.995 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise <==      Total: 8
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.088 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.088 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.089 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c1928d022db69d94
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.089 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9888cad4f87a2f68
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.089 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.089 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.089 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.090 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.184 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.185 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.185 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.185 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:25 GMT
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.185 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.185 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.185 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.185 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.186 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.186 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.194 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (102ms)
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.194 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.194 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.194 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:25 GMT
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.194 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.195 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.195 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.195 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.195 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.195 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.242 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.242 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: c1928d022db69d94
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.246 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.247 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.247 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.247 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.247 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.247 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.247 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.247 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.295 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.315 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (67ms)
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.315 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.315 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.315 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:50:25 GMT
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.315 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.316 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.316 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.316 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.316 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c1928d022db69d94,] 2025-08-12 09:50:24.316 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 19e6b05e7e3256b1
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.349 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.424 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (74ms)
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.424 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.425 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.425 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:25 GMT
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.425 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.425 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.425 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.425 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.425 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.426 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.489 DEBUG 20568 [http-nio-7003-exec-1] org.springframework.data.mongodb.core.MongoTemplate Executing count: {} in collection: enterprise_type
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.528 DEBUG 20568 [http-nio-7003-exec-1] org.springframework.data.mongodb.core.MongoTemplate find using query: {} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.type.model.EnterpriseType in collection: enterprise_type
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.335 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.337 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 372fe42df4b041af
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.337 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.337 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.337 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.337 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.339 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.340 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.425 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (84ms)
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.426 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.426 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.426 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:36 GMT
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.426 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.426 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.427 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.427 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.428 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.428 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.495 DEBUG 20568 [http-nio-7003-exec-3] org.springframework.data.mongodb.core.MongoTemplate findOne using query: { "id" : "6886dce2abe72c15d2062839"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.type.model.EnterpriseType in collection: enterprise_type
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.499 DEBUG 20568 [http-nio-7003-exec-3] org.springframework.data.mongodb.core.MongoTemplate findOne using query: { "_id" : { "$oid" : "6886dce2abe72c15d2062839"}} fields: {} in db.collection: kbc_elms_sta.enterprise_type
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.956 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.956 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.957 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 645d4376f9029950
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.958 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.958 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: e24c4e3d72c4ccb6
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.958 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.958 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.958 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.958 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.959 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.959 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.959 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.959 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:38.959 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.959 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:38.959 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.071 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (112ms)
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.071 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (112ms)
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.071 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.072 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.072 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.072 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.072 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:40 GMT
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.072 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:40 GMT
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.072 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.073 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.074 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.146 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.143 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.155 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 645d4376f9029950
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.156 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.156 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.157 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.157 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.157 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.157 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.157 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.206 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.206 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.206 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: ce624a116b4bd505
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.206 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.206 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.207 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.207 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.207 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.207 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.232 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (75ms)
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:50:40 GMT
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [645d4376f9029950,] 2025-08-12 09:50:39.233 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.285 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (76ms)
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.285 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.285 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.285 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:40 GMT
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.285 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.286 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.286 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.286 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.286 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.286 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.332 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==>  Preparing: select count(0) from (select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code) tmp_count 
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.332 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==> Parameters: T0001(String)
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.367 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT <==      Total: 1
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.368 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==>  Preparing: select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code LIMIT ? 
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.368 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==> Parameters: T0001(String), 10(Integer)
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.408 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise <==      Total: 8
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.515 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.515 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.516 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: beb4d6521dce618f
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.516 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c6d0f4c39c8fde03
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.528 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.528 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.528 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.528 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.528 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.528 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.529 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.529 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.529 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.529 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.529 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.529 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.629 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (99ms)
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.630 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.630 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.632 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:41 GMT
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.632 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.632 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.632 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.632 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.633 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.634 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.646 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (116ms)
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:41 GMT
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.647 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.649 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.696 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.696 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: beb4d6521dce618f
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.696 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.696 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.696 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.697 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.697 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.697 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.811 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (113ms)
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.813 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.813 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.813 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:50:41 GMT
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.815 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.815 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.816 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.817 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.821 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [beb4d6521dce618f,] 2025-08-12 09:50:40.822 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d07b97f822a1b24e
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.835 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.863 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.865 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.909 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (73ms)
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.909 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.909 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.909 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:41 GMT
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.910 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.910 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.910 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.910 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.910 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.924 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.933 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.980 DEBUG 20568 [http-nio-7003-exec-7] org.springframework.data.mongodb.core.MongoTemplate Executing count: {} in collection: enterprise_type
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:41.023 DEBUG 20568 [http-nio-7003-exec-7] org.springframework.data.mongodb.core.MongoTemplate find using query: {} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.type.model.EnterpriseType in collection: enterprise_type
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.217 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.219 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d980c8256c3cf729
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.219 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.219 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.219 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.219 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.219 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.220 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.313 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (93ms)
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.313 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.314 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.314 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:44 GMT
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.314 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.314 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.314 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.314 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.316 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.316 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.359 DEBUG 20568 [http-nio-7003-exec-10] org.springframework.data.mongodb.core.MongoTemplate findOne using query: { "id" : "68931c4f739d9025b7e7a662"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.type.model.EnterpriseType in collection: enterprise_type
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.359 DEBUG 20568 [http-nio-7003-exec-10] org.springframework.data.mongodb.core.MongoTemplate findOne using query: { "_id" : { "$oid" : "68931c4f739d9025b7e7a662"}} fields: {} in db.collection: kbc_elms_sta.enterprise_type
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.405 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.406 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 6ef9683aeeb6bf0d
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.406 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.407 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.407 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.407 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.407 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.407 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.483 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (75ms)
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.483 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.484 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.484 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:46 GMT
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.485 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.485 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.485 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.485 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.485 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.485 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.526 DEBUG 20568 [http-nio-7003-exec-9] org.springframework.data.mongodb.core.MongoTemplate Executing count: {} in collection: enterprise_type
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.565 DEBUG 20568 [http-nio-7003-exec-9] org.springframework.data.mongodb.core.MongoTemplate find using query: {} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.type.model.EnterpriseType in collection: enterprise_type
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.807 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.807 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.808 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5b522079fd4dd486
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.808 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: d9b09eb0d46f9bf4
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.808 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.808 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.808 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.808 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.808 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.809 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.809 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.809 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.809 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.809 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.809 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.809 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.976 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.976 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 994227a21d9c1ef4
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.976 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.976 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.976 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.976 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.978 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:48.978 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (176ms)
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (176ms)
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:49 GMT
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.986 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:49 GMT
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.987 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.987 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.987 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.987 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.987 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.987 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.987 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.988 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.988 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.988 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:48.988 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:48.989 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.032 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.032 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: 5b522079fd4dd486
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.032 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.032 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.032 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.032 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.032 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.035 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:49.038 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:49.038 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.055 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.055 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.055 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.055 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:50:49 GMT
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.055 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.056 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.056 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.056 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.056 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.056 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:49.086 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.099 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==>  Preparing: select count(0) from (select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code) tmp_count 
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.099 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==> Parameters: T0001(String)
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.113 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.113 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:50:49 GMT
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [5b522079fd4dd486,] 2025-08-12 09:50:49.114 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.141 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT <==      Total: 1
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.141 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==>  Preparing: select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code LIMIT ? 
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.142 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==> Parameters: T0001(String), 10(Integer)
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.178 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise <==      Total: 8
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.564 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.564 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.564 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 2dc001181650a09d
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.565 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.565 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: eb211f7684216ebb
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.565 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.565 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.565 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.567 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.567 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.567 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.567 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.567 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.568 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.569 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.569 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.688 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.688 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 64402118dcbaeef1
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.689 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.689 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.689 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.690 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.690 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.690 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.722 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (154ms)
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.722 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (150ms)
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.723 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.723 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.723 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.723 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.723 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:54:52 GMT
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.723 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:54:52 GMT
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.723 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.724 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.727 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.728 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.818 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (128ms)
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.818 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.819 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.819 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:54:52 GMT
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.819 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.819 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.819 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.819 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.819 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.820 DEBUG 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.823 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.823 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: eb211f7684216ebb
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.823 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.823 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.824 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.824 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.824 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:51.824 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.980 DEBUG 20568 [http-nio-7003-exec-8] org.springframework.data.mongodb.core.MongoTemplate Executing count: {} in collection: enterprise_type
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:52.011 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:52.012 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.018 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (193ms)
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.018 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:54:52 GMT
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [eb211f7684216ebb,] 2025-08-12 09:54:52.019 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:52.024 DEBUG 20568 [http-nio-7003-exec-8] org.springframework.data.mongodb.core.MongoTemplate find using query: {} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.type.model.EnterpriseType in collection: enterprise_type
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:52.066 DEBUG 20568 [http-nio-7003-exec-5] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: aa866e8db8ece9d0
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 212da2b226dcfb89
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.565 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.652 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (86ms)
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.652 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.652 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.653 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:54:52 GMT
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.653 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.653 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.653 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.653 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.653 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.653 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.655 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (89ms)
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.655 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.655 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.655 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:54:52 GMT
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.655 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.655 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.655 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.656 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.656 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.657 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: aa866e8db8ece9d0
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.700 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.704 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.705 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.755 DEBUG 20568 [http-nio-7003-exec-7] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.778 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.778 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:54:53 GMT
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [aa866e8db8ece9d0,] 2025-08-12 09:54:52.779 DEBUG 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 9ce05d24f43378b6
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:52.921 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.006 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (83ms)
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.006 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:54:53 GMT
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.007 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.050 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==>  Preparing: select count(0) from (select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code) tmp_count 
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.052 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT ==> Parameters: T0001(String)
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.088 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise_COUNT <==      Total: 1
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.090 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==>  Preparing: select op.agent_code agentCode, op.agent_name agentName, op.legal_name legalName, op.sales_center_name salesCenterName, count(distinct ae.id) agentEnterpriseNum, sum(if(ae.is_verified = '1', 1, 0)) verifiedEnterpriseNum, count(distinct op.id) opportunityNum, sum(if(op.opportunity_type = '1', 1, 0)) emplayeeOpportunityNum, sum(if(op.opportunity_type = '2', 1, 0)) generalOpportunityNum from t_opportunity op left join t_gen_agent_enterprise ae on op.agent_enterprise_id = ae.id WHERE op.is_deleted = 0 and op.tenant_id = ? group by op.agent_code, op.agent_name, op.legal_name, op.sales_center_name order by op.agent_code LIMIT ? 
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.091 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise ==> Parameters: T0001(String), 10(Integer)
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.129 DEBUG 20568 [http-nio-7003-exec-10] com.kbao.kbcelms.opportunity.dao.OpportunityMapper.statAgentEnterprise <==      Total: 8
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.817 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.819 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: b516a6514aa6411b
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.819 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.820 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.820 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.820 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.820 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.820 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.850 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.850 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: 5e90c4cc92a4ea4d
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.854 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.854 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.854 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.854 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.854 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.854 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.952 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (132ms)
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:58:28 GMT
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.953 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:27.954 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.966 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (111ms)
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.967 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.967 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.967 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:58:28 GMT
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.967 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.967 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.967 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.967 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.969 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:27.969 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.002 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.002 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: b516a6514aa6411b
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.002 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.002 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.002 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.002 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.002 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.003 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:28.051 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:28.052 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.070 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (67ms)
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.071 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.071 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.071 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:58:28 GMT
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.072 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.072 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.072 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.072 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.072 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [b516a6514aa6411b,] 2025-08-12 09:58:28.072 DEBUG 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:28.121 DEBUG 20568 [http-nio-7003-exec-3] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.495 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.496 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.497 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: a9f7eec363b4ce1a
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.497 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: fca86c6d48dd7562
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.498 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.589 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (89ms)
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.589 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.589 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.589 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:58:30 GMT
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.589 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.589 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.589 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.590 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.590 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.590 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.590 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (91ms)
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.591 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.591 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.591 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:58:30 GMT
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.591 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.592 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.592 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.592 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.596 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.596 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.634 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> POST http://kbc-bsc-web/api/nonuser/app/user/tenant/list HTTP/1.1
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.635 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] app_trace_id: a9f7eec363b4ce1a
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.635 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Length: 40
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.635 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] Content-Type: application/json
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.635 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.635 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.635 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"userId":"U000162","appId":"APP000238"}
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.636 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] ---> END HTTP (40-byte body)
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.638 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==>  Preparing: select t.user_id, t.nick_name, r.id, r.role_name, r.role_type, a.auth_code, a.auth_name from t_user t left join t_user_role ur on t.user_id = ur.user_id and ur.is_deleted = 0 left join t_role r on ur.role_id = r.id and r.tenant_id = ? left join t_role_auth ra on ur.role_id = ra.role_id and ra.is_deleted = 0 left join t_auth a on ra.auth_code = a.auth_code where t.is_deleted = 0 and t.user_id = ? 
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.638 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId ==> Parameters: T0001(String), U000162(String)
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.678 DEBUG 20568 [http-nio-7003-exec-4] com.kbao.kbcelms.user.dao.UserMapper.getUserRoleAuthByUserId <==      Total: 44
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.707 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- HTTP/1.1 200  (72ms)
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.707 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] connection: keep-alive
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.707 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.707 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] date: Tue, 12 Aug 2025 01:58:30 GMT
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.708 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.708 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.708 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] vary: accept-encoding
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.708 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] 
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.709 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] {"datas":[{"tenantId":"T0001","shortName":"大童销售","tenantName":"大童保险销售服务有限公司"}],"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [a9f7eec363b4ce1a,] 2025-08-12 09:58:29.709 DEBUG 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.client.TenantClientService [TenantClientService#getAppUserTenantList] <--- END HTTP (151-byte body)
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.163 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> POST http://kbc-bsc-web/api/nonuser/user/getUserInfo HTTP/1.1
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.163 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] app_trace_id: c8827571826480e1
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.164 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Length: 157
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.164 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] Content-Type: application/json
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.165 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] remoteIp: 127.0.0.1
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.165 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.165 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"accessToken":"fb6b977ae14bc9532ce0f686c71268e6","tenantId":"T0001","funcId":"F04010","userId":null,"phone":null,"email":null,"roleId":null,"userName":null}
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.165 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] ---> END HTTP (157-byte body)
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- HTTP/1.1 200  (77ms)
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] connection: keep-alive
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] content-type: application/json;charset=UTF-8
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] date: Tue, 12 Aug 2025 01:58:30 GMT
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] keep-alive: timeout=60
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] transfer-encoding: chunked
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] vary: accept-encoding
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.244 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] 
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.245 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] {"datas":{"user":{"userId":"U000162","userName":"ganjie","nickName":"甘杰","isAdmin":1,"tenantId":"T0001","tenantName":"大童保险销售服务有限公司","userType":"1","phone":"***********"},"function":{"funcId":"F04010","funcName":"指标列表","funcType":"2","funcLevel":3,"parentFuncId":"F04007","funcIcon":"icondt4","url":"iframe?iframeUrl=https://kbc-sta.kbao123.com/sct-algo/indexManage","urlType":"2","sort":1,"isPublic":0,"applyId":"APP000238","funcAuths":[{"authCode":"algo:indicator:show","funcId":"F04010","authName":"查看","sort":1}]},"encrypt":{"proposer":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"insured":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"beneficiary":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"order":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"},"kbUser":{"birthday":"0","name":"0","identityCode":"0","phone":"0","email":"0","workUnit":"0","contactAddress":"0","workUnitAddress":"0","bankCardNo":"0","orderCode":"0","policyNo":"0","printNo":"0","firstYearPremium":"0","enquiryPremium":"0","standardPremium":"0","visitDate":"0","underwriteDate":"0","effectiveDate":"0","receiptDate":"0","plateNumber":"0","enterpriseCert":"0","VIN":"0"}},"orgInfo":{}},"resp_code":0,"resp_msg":"查询成功"}
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.245 DEBUG 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.client.UserClientService [UserClientService#getUserInfo] <--- END HTTP (2580-byte body)
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.287 DEBUG 20568 [http-nio-7003-exec-2] org.springframework.data.mongodb.core.MongoTemplate Executing count: { "tenantId" : "T0001"} in collection: enterprise_basic_info
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.323 DEBUG 20568 [http-nio-7003-exec-2] org.springframework.data.mongodb.core.MongoTemplate find using query: { "tenantId" : "T0001"} fields: Document{{}} for class: class com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo in collection: enterprise_basic_info
