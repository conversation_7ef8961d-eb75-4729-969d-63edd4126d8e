[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:30.438 INFO 19636 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.476 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.478 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.488 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.489 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.492 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.495 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.496 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.498 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.499 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:32.500 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:33.199 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:33.250 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:31:33.252 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:35.681 INFO 19636 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:35.899 INFO 19636 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:46.178 INFO 19636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:46.181 INFO 19636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:46.449 INFO 19636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 257 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:46.480 INFO 19636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:46.484 INFO 19636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:46.759 INFO 19636 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 246 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.384 INFO 19636 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=937a3569-9901-3610-b1b2-3a073c2b99e2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.460 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.462 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.462 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.463 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.464 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.464 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.464 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.465 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.465 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.466 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.466 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.466 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.466 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.466 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.467 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:47.467 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:48.822 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:48.829 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:48.830 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:49.400 INFO 19636 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7003 (http)
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:49.433 INFO 19636 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7003"]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:49.434 INFO 19636 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:49.434 INFO 19636 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:49.655 INFO 19636 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:49.656 INFO 19636 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 13687 ms
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:51.759 INFO 19636 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:54.359 INFO 19636 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:54.856 INFO 19636 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:55.205 INFO 19636 [cluster-ClusterId{value='689a998ad51e350dafb477df', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:55.206 INFO 19636 [cluster-rtt-ClusterId{value='689a998ad51e350dafb477df', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:55.210 INFO 19636 [cluster-ClusterId{value='689a998ad51e350dafb477df', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=123360700}
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:59.684 INFO 19636 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:31:59.751 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.201 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.224 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.263 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.283 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.296 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.320 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.335 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.359 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.388 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.417 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:00.442 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.056 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-espt-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.285 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:01.870 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.024 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.105 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.346 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.374 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:02.387 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.376 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.514 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.860 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.913 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:03.982 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.019 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.115 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.156 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.218 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.276 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.323 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.410 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.497 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.591 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.660 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.706 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.741 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.769 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.795 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.829 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.859 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.888 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.915 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.941 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.967 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:04.995 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.016 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.095 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.130 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.203 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.306 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.317 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.345 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.369 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.395 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.408 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.419 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.432 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.443 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.458 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.467 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.481 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.496 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.512 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.526 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.544 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.559 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:05.576 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:06.347 INFO 19636 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:06.735 INFO 19636 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:09.016 INFO 19636 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:12.364 INFO 19636 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.611 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.636 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.664 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.690 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.712 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.734 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.752 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.771 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.792 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.816 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.846 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.881 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.908 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.931 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.954 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.960 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:14.979 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.000 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.021 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.051 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.075 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.097 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.118 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.138 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.156 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.180 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.221 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.229 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.237 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.252 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.263 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.270 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.282 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.286 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.299 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.314 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.318 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.327 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.334 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.345 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.350 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.363 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.369 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.379 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.384 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.393 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.405 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.411 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.424 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.430 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.436 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.445 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.449 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.454 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.464 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.471 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.479 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.484 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.489 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.500 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.514 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.529 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.534 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.541 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.546 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.551 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.557 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:15.562 INFO 19636 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.029 INFO 19636 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.206 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.260 INFO 19636 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.353 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.355 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.355 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.355 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.355 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.355 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:16.356 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.548 INFO 19636 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.554 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.563 INFO 19636 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1754962337562 with initial instances count: 11
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.575 INFO 19636 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.577 INFO 19636 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7003"]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.673 INFO 19636 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7003 (http) with context path ''
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.677 INFO 19636 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7003
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.678 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.678 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.678 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.679 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.679 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.679 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.679 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.679 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.679 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.679 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.680 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.680 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.680 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.680 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.680 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.681 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.681 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.681 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.681 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.745 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.746 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.746 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.746 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.746 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.746 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.746 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.746 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.747 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.747 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.747 INFO 19636 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.747 INFO 19636 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:17.839 INFO 19636 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.126 INFO 19636 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.708 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.799 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.809 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.813 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.821 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.953 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.955 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:18.966 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: updateUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.132 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.144 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.145 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: removeUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.372 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getByIdUsingGET_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.380 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getPageUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.433 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.456 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_4
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.487 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.490 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.496 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.515 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.557 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: checkCodeUsingGET_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.557 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteByIdUsingDELETE_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.558 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteByIdsUsingDELETE_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.584 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getByCodeUsingGET_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.585 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getByIdUsingGET_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.590 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getPageUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.613 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.642 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.691 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_4
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.710 INFO 19636 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:19.855 INFO 19636 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 51.587 seconds (JVM running for 54.024)
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:20.479 INFO 19636 [RMI TCP Connection(11)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:20.480 INFO 19636 [RMI TCP Connection(11)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:20.484 INFO 19636 [RMI TCP Connection(11)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 4 ms
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:32:21.236 INFO 19636 [RMI TCP Connection(14)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:37:16.377 INFO 19636 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:42:16.395 INFO 19636 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:47:16.407 INFO 19636 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:29.492 INFO 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=40d0b1b478e84c97,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [40d0b1b478e84c97,] 2025-08-12 09:47:30.508 INFO 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=40d0b1b478e84c97, 耗时=1218, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.870 INFO 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d6c0fb44b59ad5bb,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [d6c0fb44b59ad5bb,] 2025-08-12 09:47:34.933 INFO 19636 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d6c0fb44b59ad5bb, 耗时=63, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.686 INFO 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=0c4731b624e3b6c1,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [0c4731b624e3b6c1,] 2025-08-12 09:47:35.731 INFO 19636 [http-nio-7003-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=0c4731b624e3b6c1, 耗时=44, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.231 INFO 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=4c7f1927eadda760,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [4c7f1927eadda760,] 2025-08-12 09:47:36.958 INFO 19636 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=4c7f1927eadda760, 耗时=746, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754033435185,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"},{"actualCapital":"185000万人民币","actualCapitalCurrency":"人民币","alias":"小米科技","approvedTime":1732118400000,"base":"bj","businessScope":"一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口；进出口代理；通讯设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；个人卫生用品销售；卫生用品和一次性使用医疗用品销售；日用杂品销售；日用百货销售；日用品销售；化妆品批发；化妆品零售；第一类医疗器械销售；第二类医疗器械销售；玩具销售；体育用品及器材零售；体育用品及器材批发；文具用品零售；文具用品批发；鞋帽批发；鞋帽零售；服装服饰批发；服装服饰零售；钟表销售；眼镜销售（不含隐形眼镜）；针纺织品销售；家用电器销售；日用家电零售；家具销售；礼品花卉销售；农作物种子经营（仅限不再分装的包装种子）；照相机及器材销售；照相器材及望远镜批发；照相器材及望远镜零售；工艺美术品及收藏品零售（象牙及其制品除外）；工艺美术品及礼仪用品销售（象牙及其制品除外）；计算机软硬件及辅助设备零售；计算机软硬件及辅助设备批发；珠宝首饰零售；珠宝首饰批发；食用农产品批发；食用农产品零售；宠物食品及用品批发；宠物食品及用品零售；电子产品销售；摩托车及零配件零售；摩托车及零配件批发；电动自行车销售；助动自行车、代步车及零配件销售；自行车及零配件零售；自行车及零配件批发；单用途商业预付卡代理销售；商用密码产品销售；五金产品批发；五金产品零售；建筑材料销售；仪器仪表修理；计算机及办公设备维修；办公设备销售；会议及展览服务；组织文化艺术交流活动；广告设计、代理；广告制作；广告发布；摄影扩印服务；票务代理服务；通讯设备修理；移动终端设备制造；可穿戴智能设备制造。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）许可项目：第三类医疗器械经营；网络文化经营；出版物零售；出版物批发；食品销售；药品零售；广播电视节目制作经营；第一类增值电信业务；第二类增值电信业务；在线数据处理与交易处理业务（经营类电子商务）；基础电信业务；互联网信息服务；信息网络传播视听节目。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）（不得从事国家和本市产业政策禁止和限制类项目的经营活动。）","city":"北京市","companyId":23402373,"companyOrgType":"有限责任公司(自然人投资或控股)","createTime":1754035025699,"creditCode":"91110108551385082Q","district":"海淀区","districtCode":"110108","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":1267545600000,"historyNameList":["北京小米科技有限责任公司"],"historyNames":"北京小米科技有限责任公司;","id":"688c7351cc2f9762f84fced7","industry":"计算机、通信和其他电子设备制造业","industryAll":{"category":"制造业","categoryBig":"计算机、通信和其他电子设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"39","categoryCodeThird":"392","categoryMiddle":"通信设备制造","categorySmall":""},"isMicroEnt":1,"legalPersonName":"雷军","name":"小米科技有限责任公司","orgNumber":"55138508-2","percentileScore":9855,"phoneNumber":"010-69630728","property3":"Beijing Xiaomi Technology Co., Ltd.","regCapital":"185000万人民币","regCapitalCurrency":"人民币","regInstitute":"北京市海淀区市场监督管理局","regLocation":"北京市海淀区西二旗中路33号院6号楼6层006号","regNumber":"110108012660422","regStatus":"存续","socialStaffNum":37,"staffNumRange":"小于50人","tags":"存续;曾用名;投资机构;赴港上市(正常上市);小微企业;高新技术企业;独角兽;瞪羚企业;合作风险;竞争风险;司法案件;战略融资","taxNumber":"91110108551385082Q","tenantId":"T0001","type":1,"updateTime":1754035025699,"updateTimes":1753952116000,"websiteList":"https://www.mi.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.425 INFO 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=8fb36825e37fc054,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [8fb36825e37fc054,] 2025-08-12 09:47:43.479 INFO 19636 [http-nio-7003-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=8fb36825e37fc054, 耗时=54, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:43.714 INFO 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=54792ee6394ae4de,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} 
[kbc-elms-web:*********:7003] [54792ee6394ae4de,] 2025-08-12 09:47:44.576 INFO 19636 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=54792ee6394ae4de, 耗时=879, resp=null:
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:47.069 INFO 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=81dff1a0412538a9,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [81dff1a0412538a9,] 2025-08-12 09:47:47.115 INFO 19636 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=81dff1a0412538a9, 耗时=47, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.300 INFO 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=f2eaaf4bc2bf4d8d,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [f2eaaf4bc2bf4d8d,] 2025-08-12 09:47:47.473 INFO 19636 [http-nio-7003-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=f2eaaf4bc2bf4d8d, 耗时=177, resp={"datas":{"endRow":5,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":">=500000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":">=","value":500000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000},{"code":"B","createId":"U000211","createTime":1754471503000,"description":"B类公司","employeeRangeText":">=50000人","id":"68931c4f739d9025b7e7a662","name":"B类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754471503000},{"code":"C","createId":"U000211","createTime":1754881954000,"description":"","employeeRangeText":"<=5000人","id":"68995fa257eb6e16dfaf61dc","name":"C类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":5000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881954000},{"code":"D","createId":"U000211","createTime":1754881995000,"description":"","employeeRangeText":"<=500人","id":"68995fcb57eb6e16dfaf61dd","name":"D类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":500}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881995000},{"code":"E","createId":"U000211","createTime":1754882012378,"description":"","employeeRangeText":"<=50人","id":"68995fdc57eb6e16dfaf61de","name":"E类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":50}],"tenantId":"T0001","updateId":"U000211","updateTime":1754882012378}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":5,"startRow":1,"total":5},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:50.926 INFO 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c520747be448d770,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [c520747be448d770,] 2025-08-12 09:47:51.003 INFO 19636 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c520747be448d770, 耗时=77, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.119 INFO 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=91d1ed891d0f0fa8,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} 
[kbc-elms-web:*********:7003] [91d1ed891d0f0fa8,] 2025-08-12 09:47:51.155 INFO 19636 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=91d1ed891d0f0fa8, 耗时=36, resp=null:
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:48:59.931 INFO 19636 [SpringContextShutdownHook] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Unregistering application KBC-ELMS-WEB with eureka with status DOWN
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:08.401 INFO 20568 [background-preinit] org.hibernate.validator.internal.util.Version HV000001: Hibernate Validator 6.1.7.Final
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.327 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.329 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.334 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.335 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.337 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.339 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.339 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.340 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.340 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.341 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.406 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.424 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[APP_NAME_IS_UNDEFINED:*********:0000] [,] 2025-08-12 09:49:09.427 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:10.925 INFO 20568 [main] org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration Located property source: [BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms'}, BootstrapPropertySource {name='bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms'}]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:11.089 INFO 20568 [main] com.kbao.kbcelms.KbcElmsWebApplication The following profiles are active: dev
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:18.481 INFO 20568 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:18.486 INFO 20568 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:18.752 INFO 20568 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 257 ms. Found 0 MongoDB repository interfaces.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:18.773 INFO 20568 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:18.776 INFO 20568 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.030 INFO 20568 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 245 ms. Found 0 Redis repository interfaces.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.524 INFO 20568 [main] org.springframework.cloud.context.scope.GenericScope BeanFactory id=937a3569-9901-3610-b1b2-3a073c2b99e2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.575 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor Post-processing PropertySource instances
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.577 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.577 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.578 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.579 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.579 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.579 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.580 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.580 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.581 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.581 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.581 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.581 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.581 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.581 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource defaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:19.582 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:20.790 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:20.798 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:20.798 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:21.280 INFO 20568 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat initialized with port(s): 7003 (http)
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:21.303 INFO 20568 [main] org.apache.coyote.http11.Http11NioProtocol Initializing ProtocolHandler ["http-nio-7003"]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:21.304 INFO 20568 [main] org.apache.catalina.core.StandardService Starting service [Tomcat]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:21.304 INFO 20568 [main] org.apache.catalina.core.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.53]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:21.468 INFO 20568 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring embedded WebApplicationContext
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:21.468 INFO 20568 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext Root WebApplicationContext: initialization completed in 10332 ms
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:23.391 INFO 20568 [main] com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure Init DruidDataSource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:25.793 INFO 20568 [main] com.alibaba.druid.pool.DruidDataSource {dataSource-1} inited
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:26.159 INFO 20568 [main] org.mongodb.driver.cluster Cluster created with settings {hosts=[mongo-kbcs-test-lan.kbao123.com:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:26.446 INFO 20568 [cluster-ClusterId{value='689a9da69703bd6fb6ba0176', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:2, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:26.445 INFO 20568 [cluster-rtt-ClusterId{value='689a9da69703bd6fb6ba0176', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.connection Opened connection [connectionId{localValue:1, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:26.451 INFO 20568 [cluster-ClusterId{value='689a9da69703bd6fb6ba0176', description='null'}-mongo-kbcs-test-lan.kbao123.com:27017] org.mongodb.driver.cluster Monitor thread successfully connected to server with description ServerDescription{address=mongo-kbcs-test-lan.kbao123.com:27017, type=SHARD_ROUTER, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=118835900}
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.120 INFO 20568 [main] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor Autowired annotation should only be used on methods with parameters: public feign.Logger$Level com.kbao.feign.config.GolbalFeignConfig.level()
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.195 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.619 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.642 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.682 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.703 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.716 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.739 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.754 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.779 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.809 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.841 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:30.867 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.410 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-espt-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:31.618 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bpm-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:32.558 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:32.742 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:32.907 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.067 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.111 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:33.128 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.116 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ufs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.231 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-uws-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.483 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.527 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.575 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.604 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.656 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.696 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.746 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.791 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.830 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.887 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:34.958 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.013 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.045 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.075 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.103 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.132 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.158 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.194 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.230 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.267 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.300 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.336 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.372 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.407 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.436 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.536 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.580 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.656 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.740 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.751 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.787 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.810 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.839 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-custom-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.856 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.869 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.886 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.902 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.918 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.932 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.947 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.962 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.978 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:35.990 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:36.007 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:36.020 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:36.034 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:36.823 INFO 20568 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver Exposing 2 endpoint(s) beneath base path '/actuator'
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:37.236 INFO 20568 [main] springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:38.518 INFO 20568 [main] com.kbao.job.autoconfigure.XxlJobClientAutoConfigure >>>>>>>>>>> xxl-job config init.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:40.465 INFO 20568 [main] org.springframework.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration Eureka HTTP Client uses RestTemplate.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.297 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.313 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.330 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.348 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.366 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.383 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.399 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.417 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.436 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.456 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.475 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.492 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.511 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.531 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.555 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.561 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.582 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.611 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.635 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.660 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ums-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.680 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.705 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.736 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.772 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.805 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.838 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.874 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-tps-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.879 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.884 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.896 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.902 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.908 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.916 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.920 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.929 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.938 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.942 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.947 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.956 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.963 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.970 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.980 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.985 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.994 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:42.997 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.004 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.012 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.016 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.029 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.032 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.040 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.043 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.047 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.052 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.059 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.064 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.066 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.071 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.074 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.081 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.092 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.102 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.106 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.109 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-ucs-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.113 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.117 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.121 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-web' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.125 INFO 20568 [main] org.springframework.cloud.openfeign.FeignClientFactoryBean For 'kbc-bsc-api' URL not provided. Will try picking an instance via load-balancing.
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.458 INFO 20568 [main] org.springframework.cloud.netflix.eureka.InstanceInfoFactory Setting initial instance status as: STARTING
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.607 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Initializing Eureka in region us-east-1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.625 INFO 20568 [main] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.712 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Disable delta property : false
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.712 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Single vip registry refresh property : null
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.713 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Force full registry fetch : false
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.713 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Application is null : false
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.713 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Registered Applications size is zero : true
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.713 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Application version is -1: true
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:43.713 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Getting all instance registry info from the eureka server
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.824 INFO 20568 [main] com.netflix.discovery.DiscoveryClient The response status is 200
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.829 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Not registering with Eureka server per configuration
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.839 INFO 20568 [main] com.netflix.discovery.DiscoveryClient Discovery Client initialized at timestamp 1754963384836 with initial instances count: 13
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.850 INFO 20568 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry Registering application KBC-ELMS-WEB with eureka with status UP
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.853 INFO 20568 [main] org.apache.coyote.http11.Http11NioProtocol Starting ProtocolHandler ["http-nio-7003"]
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.931 INFO 20568 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer Tomcat started on port(s): 7003 (http) with context path ''
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.934 INFO 20568 [main] org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration Updating port to 7003
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.942 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.942 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar-dev.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.942 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar.yml,group-kbc-elms refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.943 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source bootstrapProperties-sta-kbcs-app-elms-web-jar,group-kbc-elms refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.943 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.943 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.944 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.944 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.944 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.944 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.944 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.944 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.944 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudDefaultProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.945 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.945 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.945 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.945 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.945 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:44.945 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.011 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.RefreshScopeRefreshedEventListener Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.012 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.013 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source systemEnvironment refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.013 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source random refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.013 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source cachedrandom refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.013 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source springCloudClientHostInfo refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.013 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.013 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.caching.CachingDelegateEncryptablePropertySource Property Source defaultProperties refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.014 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.014 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.014 INFO 20568 [main] com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.014 INFO 20568 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Context refreshed
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.102 INFO 20568 [main] springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:45.535 INFO 20568 [main] springfox.documentation.spring.web.scanners.ApiListingReferenceScanner Scanning for api listing references
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.100 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.199 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.211 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.215 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.225 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.385 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.387 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_3
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.399 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: updateUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.584 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: addUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.599 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.601 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: removeUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.722 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getByIdUsingGET_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.727 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getPageUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.748 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.759 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteUsingPOST_4
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.765 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.766 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: listUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.771 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_3
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.776 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.793 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: checkCodeUsingGET_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.793 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteByIdUsingDELETE_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.794 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: deleteByIdsUsingDELETE_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.813 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getByCodeUsingGET_1
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.816 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getByIdUsingGET_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.823 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: getPageUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.843 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.868 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: detailUsingPOST_3
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.907 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: pageUsingPOST_4
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:46.919 INFO 20568 [main] springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator Generating unique operation named: saveOrUpdateUsingPOST_2
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:47.046 INFO 20568 [main] com.kbao.kbcelms.KbcElmsWebApplication Started KbcElmsWebApplication in 40.325 seconds (JVM running for 42.228)
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:47.649 INFO 20568 [RMI TCP Connection(5)-*********] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] Initializing Spring DispatcherServlet 'dispatcherServlet'
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:47.650 INFO 20568 [RMI TCP Connection(5)-*********] org.springframework.web.servlet.DispatcherServlet Initializing Servlet 'dispatcherServlet'
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:47.687 INFO 20568 [RMI TCP Connection(5)-*********] org.springframework.web.servlet.DispatcherServlet Completed initialization in 37 ms
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:49:48.652 INFO 20568 [RMI TCP Connection(6)-*********] org.mongodb.driver.connection Opened connection [connectionId{localValue:3, serverValue:2147483647}] to mongo-kbcs-test-lan.kbao123.com:27017
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:58.643 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=7f271ea68e3b6ddb,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:58.643 INFO 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c09451d24e972b7d,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:58.962 INFO 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=493b7a2f4d694425,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [c09451d24e972b7d,] 2025-08-12 09:49:59.372 INFO 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c09451d24e972b7d, 耗时=1070, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [7f271ea68e3b6ddb,] 2025-08-12 09:49:59.372 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=7f271ea68e3b6ddb, 耗时=1070, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [493b7a2f4d694425,] 2025-08-12 09:49:59.652 INFO 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=493b7a2f4d694425, 耗时=1290, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754033435185,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"},{"actualCapital":"185000万人民币","actualCapitalCurrency":"人民币","alias":"小米科技","approvedTime":1732118400000,"base":"bj","businessScope":"一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口；进出口代理；通讯设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；个人卫生用品销售；卫生用品和一次性使用医疗用品销售；日用杂品销售；日用百货销售；日用品销售；化妆品批发；化妆品零售；第一类医疗器械销售；第二类医疗器械销售；玩具销售；体育用品及器材零售；体育用品及器材批发；文具用品零售；文具用品批发；鞋帽批发；鞋帽零售；服装服饰批发；服装服饰零售；钟表销售；眼镜销售（不含隐形眼镜）；针纺织品销售；家用电器销售；日用家电零售；家具销售；礼品花卉销售；农作物种子经营（仅限不再分装的包装种子）；照相机及器材销售；照相器材及望远镜批发；照相器材及望远镜零售；工艺美术品及收藏品零售（象牙及其制品除外）；工艺美术品及礼仪用品销售（象牙及其制品除外）；计算机软硬件及辅助设备零售；计算机软硬件及辅助设备批发；珠宝首饰零售；珠宝首饰批发；食用农产品批发；食用农产品零售；宠物食品及用品批发；宠物食品及用品零售；电子产品销售；摩托车及零配件零售；摩托车及零配件批发；电动自行车销售；助动自行车、代步车及零配件销售；自行车及零配件零售；自行车及零配件批发；单用途商业预付卡代理销售；商用密码产品销售；五金产品批发；五金产品零售；建筑材料销售；仪器仪表修理；计算机及办公设备维修；办公设备销售；会议及展览服务；组织文化艺术交流活动；广告设计、代理；广告制作；广告发布；摄影扩印服务；票务代理服务；通讯设备修理；移动终端设备制造；可穿戴智能设备制造。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）许可项目：第三类医疗器械经营；网络文化经营；出版物零售；出版物批发；食品销售；药品零售；广播电视节目制作经营；第一类增值电信业务；第二类增值电信业务；在线数据处理与交易处理业务（经营类电子商务）；基础电信业务；互联网信息服务；信息网络传播视听节目。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）（不得从事国家和本市产业政策禁止和限制类项目的经营活动。）","city":"北京市","companyId":23402373,"companyOrgType":"有限责任公司(自然人投资或控股)","createTime":1754035025699,"creditCode":"91110108551385082Q","district":"海淀区","districtCode":"110108","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":1267545600000,"historyNameList":["北京小米科技有限责任公司"],"historyNames":"北京小米科技有限责任公司;","id":"688c7351cc2f9762f84fced7","industry":"计算机、通信和其他电子设备制造业","industryAll":{"category":"制造业","categoryBig":"计算机、通信和其他电子设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"39","categoryCodeThird":"392","categoryMiddle":"通信设备制造","categorySmall":""},"isMicroEnt":1,"legalPersonName":"雷军","name":"小米科技有限责任公司","orgNumber":"55138508-2","percentileScore":9855,"phoneNumber":"010-69630728","property3":"Beijing Xiaomi Technology Co., Ltd.","regCapital":"185000万人民币","regCapitalCurrency":"人民币","regInstitute":"北京市海淀区市场监督管理局","regLocation":"北京市海淀区西二旗中路33号院6号楼6层006号","regNumber":"110108012660422","regStatus":"存续","socialStaffNum":37,"staffNumRange":"小于50人","tags":"存续;曾用名;投资机构;赴港上市(正常上市);小微企业;高新技术企业;独角兽;瞪羚企业;合作风险;竞争风险;司法案件;战略融资","taxNumber":"91110108551385082Q","tenantId":"T0001","type":1,"updateTime":1754035025699,"updateTimes":1753952116000,"websiteList":"https://www.mi.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:19.964 INFO 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=93280e6a7d14601e,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [93280e6a7d14601e,] 2025-08-12 09:50:20.012 INFO 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=93280e6a7d14601e, 耗时=47, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.250 INFO 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c0ee7792c0220169,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} 
[kbc-elms-web:*********:7003] [c0ee7792c0220169,] 2025-08-12 09:50:20.998 INFO 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c0ee7792c0220169, 耗时=752, resp={"datas":{"endRow":8,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"agentCode":"640172502","agentEnterpriseNum":0,"agentName":"沈国强","emplayeeOpportunityNum":2,"generalOpportunityNum":0,"legalName":"大童上海法人公司","opportunityNum":2,"salesCenterName":"大童上海市区扬紫销售团队","verifiedEnterpriseNum":0},{"agentCode":"AGT002","agentEnterpriseNum":0,"agentName":"李四","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"上海总公司","opportunityNum":1,"salesCenterName":"上海销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT003","agentEnterpriseNum":0,"agentName":"王五","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"广州总公司","opportunityNum":1,"salesCenterName":"广州销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT004","agentEnterpriseNum":1,"agentName":"赵六","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"深圳总公司","opportunityNum":1,"salesCenterName":"深圳销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT005","agentEnterpriseNum":1,"agentName":"孙七","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"成都总公司","opportunityNum":1,"salesCenterName":"成都销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT006","agentEnterpriseNum":1,"agentName":"周八","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"武汉总公司","opportunityNum":1,"salesCenterName":"武汉销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT007","agentEnterpriseNum":1,"agentName":"吴九","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"西安总公司","opportunityNum":1,"salesCenterName":"西安销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT008","agentEnterpriseNum":1,"agentName":"郑十","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"杭州总公司","opportunityNum":1,"salesCenterName":"杭州销售中心","verifiedEnterpriseNum":1}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":8,"startRow":1,"total":8},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.246 INFO 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9888cad4f87a2f68,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [9888cad4f87a2f68,] 2025-08-12 09:50:24.296 INFO 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9888cad4f87a2f68, 耗时=50, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.471 INFO 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=19e6b05e7e3256b1,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [19e6b05e7e3256b1,] 2025-08-12 09:50:24.629 INFO 20568 [http-nio-7003-exec-1] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=19e6b05e7e3256b1, 耗时=161, resp={"datas":{"endRow":5,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":">=500000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":">=","value":500000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000},{"code":"B","createId":"U000211","createTime":1754471503000,"description":"B类公司","employeeRangeText":">=50000人","id":"68931c4f739d9025b7e7a662","name":"B类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754471503000},{"code":"C","createId":"U000211","createTime":1754881954000,"description":"","employeeRangeText":"<=5000人","id":"68995fa257eb6e16dfaf61dc","name":"C类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":5000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881954000},{"code":"D","createId":"U000211","createTime":1754881995000,"description":"","employeeRangeText":"<=500人","id":"68995fcb57eb6e16dfaf61dd","name":"D类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":500}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881995000},{"code":"E","createId":"U000211","createTime":1754882012378,"description":"","employeeRangeText":"<=50人","id":"68995fdc57eb6e16dfaf61de","name":"E类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":50}],"tenantId":"T0001","updateId":"U000211","updateTime":1754882012378}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":5,"startRow":1,"total":5},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.482 INFO 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=372fe42df4b041af,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/detail , httpMethod=null, reqData={"param1":{"id":"6886dce2abe72c15d2062839"}} 
[kbc-elms-web:*********:7003] [372fe42df4b041af,] 2025-08-12 09:50:35.540 INFO 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=372fe42df4b041af, 耗时=58, resp={"datas":{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"rules":[{"field":"employeeCount","operator":">=","value":500000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.140 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=e24c4e3d72c4ccb6,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [e24c4e3d72c4ccb6,] 2025-08-12 09:50:39.208 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=e24c4e3d72c4ccb6, 耗时=68, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.330 INFO 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=ce624a116b4bd505,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} 
[kbc-elms-web:*********:7003] [ce624a116b4bd505,] 2025-08-12 09:50:39.409 INFO 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=ce624a116b4bd505, 耗时=79, resp={"datas":{"endRow":8,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"agentCode":"640172502","agentEnterpriseNum":0,"agentName":"沈国强","emplayeeOpportunityNum":2,"generalOpportunityNum":0,"legalName":"大童上海法人公司","opportunityNum":2,"salesCenterName":"大童上海市区扬紫销售团队","verifiedEnterpriseNum":0},{"agentCode":"AGT002","agentEnterpriseNum":0,"agentName":"李四","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"上海总公司","opportunityNum":1,"salesCenterName":"上海销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT003","agentEnterpriseNum":0,"agentName":"王五","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"广州总公司","opportunityNum":1,"salesCenterName":"广州销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT004","agentEnterpriseNum":1,"agentName":"赵六","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"深圳总公司","opportunityNum":1,"salesCenterName":"深圳销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT005","agentEnterpriseNum":1,"agentName":"孙七","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"成都总公司","opportunityNum":1,"salesCenterName":"成都销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT006","agentEnterpriseNum":1,"agentName":"周八","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"武汉总公司","opportunityNum":1,"salesCenterName":"武汉销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT007","agentEnterpriseNum":1,"agentName":"吴九","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"西安总公司","opportunityNum":1,"salesCenterName":"西安销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT008","agentEnterpriseNum":1,"agentName":"郑十","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"杭州总公司","opportunityNum":1,"salesCenterName":"杭州销售中心","verifiedEnterpriseNum":1}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":8,"startRow":1,"total":8},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.733 INFO 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c6d0f4c39c8fde03,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [c6d0f4c39c8fde03,] 2025-08-12 09:50:40.934 INFO 20568 [http-nio-7003-exec-6] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c6d0f4c39c8fde03, 耗时=201, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:40.980 INFO 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d07b97f822a1b24e,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [d07b97f822a1b24e,] 2025-08-12 09:50:41.063 INFO 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d07b97f822a1b24e, 耗时=83, resp={"datas":{"endRow":5,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":">=500000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":">=","value":500000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000},{"code":"B","createId":"U000211","createTime":1754471503000,"description":"B类公司","employeeRangeText":">=50000人","id":"68931c4f739d9025b7e7a662","name":"B类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754471503000},{"code":"C","createId":"U000211","createTime":1754881954000,"description":"","employeeRangeText":"<=5000人","id":"68995fa257eb6e16dfaf61dc","name":"C类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":5000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881954000},{"code":"D","createId":"U000211","createTime":1754881995000,"description":"","employeeRangeText":"<=500人","id":"68995fcb57eb6e16dfaf61dd","name":"D类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":500}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881995000},{"code":"E","createId":"U000211","createTime":1754882012378,"description":"","employeeRangeText":"<=50人","id":"68995fdc57eb6e16dfaf61de","name":"E类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":50}],"tenantId":"T0001","updateId":"U000211","updateTime":1754882012378}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":5,"startRow":1,"total":5},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.358 INFO 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d980c8256c3cf729,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/detail , httpMethod=null, reqData={"param1":{"id":"68931c4f739d9025b7e7a662"}} 
[kbc-elms-web:*********:7003] [d980c8256c3cf729,] 2025-08-12 09:50:43.397 INFO 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d980c8256c3cf729, 耗时=39, resp={"datas":{"code":"B","createId":"U000211","createTime":1754471503000,"description":"B类公司","id":"68931c4f739d9025b7e7a662","name":"B类","priority":1,"rules":[{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754471503000},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.526 INFO 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=6ef9683aeeb6bf0d,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [6ef9683aeeb6bf0d,] 2025-08-12 09:50:46.605 INFO 20568 [http-nio-7003-exec-9] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=6ef9683aeeb6bf0d, 耗时=79, resp={"datas":{"endRow":5,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":">=500000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":">=","value":500000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000},{"code":"B","createId":"U000211","createTime":1754471503000,"description":"B类公司","employeeRangeText":">=50000人","id":"68931c4f739d9025b7e7a662","name":"B类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754471503000},{"code":"C","createId":"U000211","createTime":1754881954000,"description":"","employeeRangeText":"<=5000人","id":"68995fa257eb6e16dfaf61dc","name":"C类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":5000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881954000},{"code":"D","createId":"U000211","createTime":1754881995000,"description":"","employeeRangeText":"<=500人","id":"68995fcb57eb6e16dfaf61dd","name":"D类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":500}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881995000},{"code":"E","createId":"U000211","createTime":1754882012378,"description":"","employeeRangeText":"<=50人","id":"68995fdc57eb6e16dfaf61de","name":"E类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":50}],"tenantId":"T0001","updateId":"U000211","updateTime":1754882012378}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":5,"startRow":1,"total":5},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:49.037 INFO 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=d9b09eb0d46f9bf4,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [d9b09eb0d46f9bf4,] 2025-08-12 09:50:49.086 INFO 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=d9b09eb0d46f9bf4, 耗时=49, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.097 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=994227a21d9c1ef4,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} 
[kbc-elms-web:*********:7003] [994227a21d9c1ef4,] 2025-08-12 09:50:49.178 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=994227a21d9c1ef4, 耗时=81, resp={"datas":{"endRow":8,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"agentCode":"640172502","agentEnterpriseNum":0,"agentName":"沈国强","emplayeeOpportunityNum":2,"generalOpportunityNum":0,"legalName":"大童上海法人公司","opportunityNum":2,"salesCenterName":"大童上海市区扬紫销售团队","verifiedEnterpriseNum":0},{"agentCode":"AGT002","agentEnterpriseNum":0,"agentName":"李四","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"上海总公司","opportunityNum":1,"salesCenterName":"上海销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT003","agentEnterpriseNum":0,"agentName":"王五","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"广州总公司","opportunityNum":1,"salesCenterName":"广州销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT004","agentEnterpriseNum":1,"agentName":"赵六","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"深圳总公司","opportunityNum":1,"salesCenterName":"深圳销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT005","agentEnterpriseNum":1,"agentName":"孙七","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"成都总公司","opportunityNum":1,"salesCenterName":"成都销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT006","agentEnterpriseNum":1,"agentName":"周八","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"武汉总公司","opportunityNum":1,"salesCenterName":"武汉销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT007","agentEnterpriseNum":1,"agentName":"吴九","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"西安总公司","opportunityNum":1,"salesCenterName":"西安销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT008","agentEnterpriseNum":1,"agentName":"郑十","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"杭州总公司","opportunityNum":1,"salesCenterName":"杭州销售中心","verifiedEnterpriseNum":1}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":8,"startRow":1,"total":8},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:54:43.725 INFO 20568 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:51.766 INFO 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=2dc001181650a09d,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:51.979 INFO 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=64402118dcbaeef1,  url=com.kbao.kbcelms.controller.enterprise.type.EnterpriseTypeController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [64402118dcbaeef1,] 2025-08-12 09:54:52.066 INFO 20568 [http-nio-7003-exec-8] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=64402118dcbaeef1, 耗时=86, resp={"datas":{"endRow":5,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"code":"A","createId":"U000162","createTime":1753668834000,"description":"","employeeRangeText":">=500000人","id":"6886dce2abe72c15d2062839","name":"A类","priority":1,"revenueRangeText":">=600000万元","rules":[{"field":"employeeCount","operator":">=","value":500000},{"field":"revenue","operator":">=","value":6000000000}],"tenantId":"T0001","updateId":"U000162","updateTime":1753668834000},{"code":"B","createId":"U000211","createTime":1754471503000,"description":"B类公司","employeeRangeText":">=50000人","id":"68931c4f739d9025b7e7a662","name":"B类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":">=","value":50000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754471503000},{"code":"C","createId":"U000211","createTime":1754881954000,"description":"","employeeRangeText":"<=5000人","id":"68995fa257eb6e16dfaf61dc","name":"C类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":5000}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881954000},{"code":"D","createId":"U000211","createTime":1754881995000,"description":"","employeeRangeText":"<=500人","id":"68995fcb57eb6e16dfaf61dd","name":"D类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":500}],"tenantId":"T0001","updateId":"U000211","updateTime":1754881995000},{"code":"E","createId":"U000211","createTime":1754882012378,"description":"","employeeRangeText":"<=50人","id":"68995fdc57eb6e16dfaf61de","name":"E类","priority":1,"revenueRangeText":"-","rules":[{"field":"employeeCount","operator":"<=","value":50}],"tenantId":"T0001","updateId":"U000211","updateTime":1754882012378}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":5,"startRow":1,"total":5},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [2dc001181650a09d,] 2025-08-12 09:54:52.066 INFO 20568 [http-nio-7003-exec-5] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=2dc001181650a09d, 耗时=300, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.703 INFO 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=212da2b226dcfb89,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [212da2b226dcfb89,] 2025-08-12 09:54:52.756 INFO 20568 [http-nio-7003-exec-7] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=212da2b226dcfb89, 耗时=53, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.048 INFO 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=9ce05d24f43378b6,  url=com.kbao.kbcelms.controller.genAgentEnterprise.GenAgentEnterpriseController/statAgentEnterprise , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{"agentCode":"","agentName":""}}} 
[kbc-elms-web:*********:7003] [9ce05d24f43378b6,] 2025-08-12 09:54:53.129 INFO 20568 [http-nio-7003-exec-10] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=9ce05d24f43378b6, 耗时=81, resp={"datas":{"endRow":8,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"agentCode":"640172502","agentEnterpriseNum":0,"agentName":"沈国强","emplayeeOpportunityNum":2,"generalOpportunityNum":0,"legalName":"大童上海法人公司","opportunityNum":2,"salesCenterName":"大童上海市区扬紫销售团队","verifiedEnterpriseNum":0},{"agentCode":"AGT002","agentEnterpriseNum":0,"agentName":"李四","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"上海总公司","opportunityNum":1,"salesCenterName":"上海销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT003","agentEnterpriseNum":0,"agentName":"王五","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"广州总公司","opportunityNum":1,"salesCenterName":"广州销售中心","verifiedEnterpriseNum":0},{"agentCode":"AGT004","agentEnterpriseNum":1,"agentName":"赵六","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"深圳总公司","opportunityNum":1,"salesCenterName":"深圳销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT005","agentEnterpriseNum":1,"agentName":"孙七","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"成都总公司","opportunityNum":1,"salesCenterName":"成都销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT006","agentEnterpriseNum":1,"agentName":"周八","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"武汉总公司","opportunityNum":1,"salesCenterName":"武汉销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT007","agentEnterpriseNum":1,"agentName":"吴九","emplayeeOpportunityNum":1,"generalOpportunityNum":0,"legalName":"西安总公司","opportunityNum":1,"salesCenterName":"西安销售中心","verifiedEnterpriseNum":1},{"agentCode":"AGT008","agentEnterpriseNum":1,"agentName":"郑十","emplayeeOpportunityNum":0,"generalOpportunityNum":1,"legalName":"杭州总公司","opportunityNum":1,"salesCenterName":"杭州销售中心","verifiedEnterpriseNum":1}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":8,"startRow":1,"total":8},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:28.010 INFO 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=5e90c4cc92a4ea4d,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [5e90c4cc92a4ea4d,] 2025-08-12 09:58:28.123 INFO 20568 [http-nio-7003-exec-3] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=5e90c4cc92a4ea4d, 耗时=113, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.637 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=fca86c6d48dd7562,  url=com.kbao.kbcelms.controller.user.UserController/getCurrentUserAuth , httpMethod=null, reqData={} 
[kbc-elms-web:*********:7003] [fca86c6d48dd7562,] 2025-08-12 09:58:29.679 INFO 20568 [http-nio-7003-exec-4] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=fca86c6d48dd7562, 耗时=42, resp={"datas":{"roleAuthStr":"elms:opportunity:receive,elms:opportunity:close:nullity,elms:opportunity:details:customer,elms:opportunity:details:basic,elms:opportunity:details:progress,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download,elms:opportunity:stop,elms:opportunity:close:nullity,elms:opportunity:restart:pause,elms:opportunity:restart:fail,elms:opportunity:details:basic,elms:opportunity:details:customer,elms:opportunity:details:progress,elms:opportunity:log:view,elms:opportunity:log:edit,elms:opportunity:log:delete,elms:opportunity:log:add,elms:opportunity:summary:download,elms:opportunity:member:team:view:total,elms:opportunity:member:divide:view:total,elms:opportunity:summary:upload,elms:opportunity:summary:delete,elms:opportunity:data:ecology:view,elms:opportunity:data:bidding:view,elms:opportunity:data:bidding:download,elms:opportunity:data:authorization:view,elms:opportunity:data:authorization:download,elms:opportunity:data:result:view,elms:opportunity:data:result:download","userId":"U000162","userName":"甘杰","userRoles":[{"roleAuths":[{"authCode":"elms:opportunity:receive","authName":"领取机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":1,"roleName":"分公司统筹角色","roleType":"1"},{"roleAuths":[{"authCode":"elms:opportunity:stop","authName":"暂停机会"},{"authCode":"elms:opportunity:close:nullity","authName":"无效"},{"authCode":"elms:opportunity:restart:pause","authName":"暂停重启"},{"authCode":"elms:opportunity:restart:fail","authName":"推进失败重启"},{"authCode":"elms:opportunity:details:basic","authName":"基础信息"},{"authCode":"elms:opportunity:details:customer","authName":"客户信息"},{"authCode":"elms:opportunity:details:progress","authName":"机会进度"},{"authCode":"elms:opportunity:log:view","authName":"查看"},{"authCode":"elms:opportunity:log:edit","authName":"编辑"},{"authCode":"elms:opportunity:log:delete","authName":"删除"},{"authCode":"elms:opportunity:log:add","authName":"添加"},{"authCode":"elms:opportunity:summary:download","authName":"下载"},{"authCode":"elms:opportunity:member:team:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:member:divide:view:total","authName":"可见全部人员"},{"authCode":"elms:opportunity:summary:upload","authName":"上传"},{"authCode":"elms:opportunity:summary:delete","authName":"删除"},{"authCode":"elms:opportunity:data:ecology:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:view","authName":"查看"},{"authCode":"elms:opportunity:data:bidding:download","authName":"下载"},{"authCode":"elms:opportunity:data:authorization:view","authName":"查看"},{"authCode":"elms:opportunity:data:authorization:download","authName":"下载"},{"authCode":"elms:opportunity:data:result:view","authName":"查看"},{"authCode":"elms:opportunity:data:result:download","authName":"下载"}],"roleId":15,"roleName":"总公司统筹角色","roleType":"2"}]},"resp_code":0,"resp_msg":"查询成功"}:
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.286 INFO 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 开始请求，transId=c8827571826480e1,  url=com.kbao.kbcelms.controller.enterprise.base.EnterpriseBaseController/page , httpMethod=null, reqData={"param1":{"pageNum":1,"pageSize":10,"param":{}}} 
[kbc-elms-web:*********:7003] [c8827571826480e1,] 2025-08-12 09:58:30.365 INFO 20568 [http-nio-7003-exec-2] com.kbao.kbcbsc.log.aop.LogAnnotationAOP 请求完成, transId=c8827571826480e1, 耗时=79, resp={"datas":{"endRow":2,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"actualCapital":"20217.5636万人民币","actualCapitalCurrency":"人民币","alias":"百达精工","approvedTime":1749744000000,"base":"zj","bondName":"百达精工","bondNum":"603331","bondType":"A股","businessScope":"空调压缩机、冰箱压缩机、空气压缩机及设备配件、汽车零配件（不含发动机）、五金机械电器配件制造、加工、销售。","city":"台州市","companyId":44302802,"companyOrgType":"其他股份有限公司(上市)","createTime":1754011649790,"creditCode":"913310007200456372","district":"椒江区","districtCode":"331002","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":965577600000,"historyNameList":["台州市百达制冷有限公司"],"historyNames":"台州市百达制冷有限公司;","id":"688c1802bf13d11683ba16ba","industry":"通用设备制造业","industryAll":{"category":"制造业","categoryBig":"通用设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"34","categoryCodeThird":"346","categoryMiddle":"烘炉、风机、包装等设备制造","categorySmall":""},"isMicroEnt":0,"legalPersonName":"施小友","name":"浙江百达精工股份有限公司","orgNumber":"72004563-7","percentileScore":9267,"phoneNumber":"0576-88488860","property3":"Zhejiang Baida Precision Manufacturing Corp.","regCapital":"20217.5636万人民币","regCapitalCurrency":"人民币","regInstitute":"浙江省市场监督管理局","regLocation":"浙江省台州市台州湾新区海城路2399号","regNumber":"331000000000905","regStatus":"存续","scale":"大型","socialStaffNum":1246,"staffNumRange":"1000-4999人","tags":"存续;曾用名;投资机构;A股(正常上市);高新技术企业;专精特新中小企业;企业技术中心;火炬计划;股权质押;司法案件","taxNumber":"913310007200456372","tenantId":"T0001","toTime":253392422400000,"type":1,"updateTime":1754033435185,"updateTimes":1752112735000,"usedBondName":"","websiteList":"http://www.baidapm.com"},{"actualCapital":"185000万人民币","actualCapitalCurrency":"人民币","alias":"小米科技","approvedTime":1732118400000,"base":"bj","businessScope":"一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口；进出口代理；通讯设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；个人卫生用品销售；卫生用品和一次性使用医疗用品销售；日用杂品销售；日用百货销售；日用品销售；化妆品批发；化妆品零售；第一类医疗器械销售；第二类医疗器械销售；玩具销售；体育用品及器材零售；体育用品及器材批发；文具用品零售；文具用品批发；鞋帽批发；鞋帽零售；服装服饰批发；服装服饰零售；钟表销售；眼镜销售（不含隐形眼镜）；针纺织品销售；家用电器销售；日用家电零售；家具销售；礼品花卉销售；农作物种子经营（仅限不再分装的包装种子）；照相机及器材销售；照相器材及望远镜批发；照相器材及望远镜零售；工艺美术品及收藏品零售（象牙及其制品除外）；工艺美术品及礼仪用品销售（象牙及其制品除外）；计算机软硬件及辅助设备零售；计算机软硬件及辅助设备批发；珠宝首饰零售；珠宝首饰批发；食用农产品批发；食用农产品零售；宠物食品及用品批发；宠物食品及用品零售；电子产品销售；摩托车及零配件零售；摩托车及零配件批发；电动自行车销售；助动自行车、代步车及零配件销售；自行车及零配件零售；自行车及零配件批发；单用途商业预付卡代理销售；商用密码产品销售；五金产品批发；五金产品零售；建筑材料销售；仪器仪表修理；计算机及办公设备维修；办公设备销售；会议及展览服务；组织文化艺术交流活动；广告设计、代理；广告制作；广告发布；摄影扩印服务；票务代理服务；通讯设备修理；移动终端设备制造；可穿戴智能设备制造。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）许可项目：第三类医疗器械经营；网络文化经营；出版物零售；出版物批发；食品销售；药品零售；广播电视节目制作经营；第一类增值电信业务；第二类增值电信业务；在线数据处理与交易处理业务（经营类电子商务）；基础电信业务；互联网信息服务；信息网络传播视听节目。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）（不得从事国家和本市产业政策禁止和限制类项目的经营活动。）","city":"北京市","companyId":23402373,"companyOrgType":"有限责任公司(自然人投资或控股)","createTime":1754035025699,"creditCode":"91110108551385082Q","district":"海淀区","districtCode":"110108","email":"<EMAIL>","emailList":["<EMAIL>","<EMAIL>","<EMAIL>"],"fromTime":1267545600000,"historyNameList":["北京小米科技有限责任公司"],"historyNames":"北京小米科技有限责任公司;","id":"688c7351cc2f9762f84fced7","industry":"计算机、通信和其他电子设备制造业","industryAll":{"category":"制造业","categoryBig":"计算机、通信和其他电子设备制造业","categoryCodeFirst":"C","categoryCodeSecond":"39","categoryCodeThird":"392","categoryMiddle":"通信设备制造","categorySmall":""},"isMicroEnt":1,"legalPersonName":"雷军","name":"小米科技有限责任公司","orgNumber":"55138508-2","percentileScore":9855,"phoneNumber":"010-69630728","property3":"Beijing Xiaomi Technology Co., Ltd.","regCapital":"185000万人民币","regCapitalCurrency":"人民币","regInstitute":"北京市海淀区市场监督管理局","regLocation":"北京市海淀区西二旗中路33号院6号楼6层006号","regNumber":"110108012660422","regStatus":"存续","socialStaffNum":37,"staffNumRange":"小于50人","tags":"存续;曾用名;投资机构;赴港上市(正常上市);小微企业;高新技术企业;独角兽;瞪羚企业;合作风险;竞争风险;司法案件;战略融资","taxNumber":"91110108551385082Q","tenantId":"T0001","type":1,"updateTime":1754035025699,"updateTimes":1753952116000,"websiteList":"https://www.mi.com"}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":2,"startRow":1,"total":2},"resp_code":0,"resp_msg":"请求成功"}:
[kbc-elms-web:*********:7003] [,] 2025-08-12 09:59:43.740 INFO 20568 [AsyncResolver-bootstrap-executor-0] com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver Resolving eureka endpoints via configuration
