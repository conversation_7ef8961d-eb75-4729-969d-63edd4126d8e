package com.kbao.kbcelms.web.handler;

import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理整个应用的异常，避免异常信息直接暴露给前端
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@ControllerAdvice
@ResponseBody
public class ElmsGlobalExceptionHandler {

    /**
     * 处理业务异常
     *
     * @param e 业务异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleBusinessException(BusinessException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.warn("业务异常 - 请求URI: {}, 异常信息: {}", requestUri, e.getMessage(), e);

        // BusinessException通常包含用户友好的错误信息，直接返回
        return Result.failed(e.getMessage());
    }

    /**
     * 处理参数校验异常 - @Valid注解校验失败
     * 
     * @param e 方法参数校验异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        
        // 收集所有字段错误信息
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        log.warn("参数校验异常 - 请求URI: {}, 校验错误: {}", requestUri, errorMessage);
        return Result.failed("参数校验失败: " + errorMessage);
    }

    /**
     * 处理参数绑定异常 - @Validated注解校验失败
     * 
     * @param e 参数绑定异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleBindException(BindException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        
        // 收集所有字段错误信息
        String errorMessage = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));
        
        log.warn("参数绑定异常 - 请求URI: {}, 绑定错误: {}", requestUri, errorMessage);
        return Result.failed("参数绑定失败: " + errorMessage);
    }

    /**
     * 处理约束违反异常 - Bean Validation校验失败
     * 
     * @param e 约束违反异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        
        // 收集所有约束违反信息
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));
        
        log.warn("约束违反异常 - 请求URI: {}, 约束错误: {}", requestUri, errorMessage);
        return Result.failed("约束校验失败: " + errorMessage);
    }

    /**
     * 处理通用校验异常
     * 
     * @param e 校验异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleValidationException(ValidationException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.warn("校验异常 - 请求URI: {}, 异常信息: {}", requestUri, e.getMessage(), e);
        return Result.failed("校验失败: " + e.getMessage());
    }

    /**
     * 处理非法参数异常
     * 
     * @param e 非法参数异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.warn("非法参数异常 - 请求URI: {}, 异常信息: {}", requestUri, e.getMessage(), e);
        return Result.failed("参数错误: " + e.getMessage());
    }

    /**
     * 处理空指针异常
     * 
     * @param e 空指针异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.error("空指针异常 - 请求URI: {}, 异常信息: {}", requestUri, e.getMessage(), e);
        return Result.failed("系统内部错误，请联系管理员");
    }

    /**
     * 处理运行时异常
     * 
     * @param e 运行时异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.error("运行时异常 - 请求URI: {}, 异常信息: {}", requestUri, e.getMessage(), e);
        
        // 对于运行时异常，不直接暴露具体错误信息给前端
        return Result.failed("系统处理异常，请稍后重试");
    }

    /**
     * 处理通用异常 - 兜底处理
     * 
     * @param e 通用异常
     * @param request HTTP请求
     * @return 统一错误响应
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleException(Exception e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.error("系统异常 - 请求URI: {}, 异常类型: {}, 异常信息: {}", 
                requestUri, e.getClass().getSimpleName(), e.getMessage(), e);
        
        // 对于未知异常，返回通用错误信息
        return Result.failed("系统异常，请联系管理员");
    }


}
