package com.kbao.kbcelms.web.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.web.Result;
import com.kbao.kbcelms.constant.annotation.ElmsAuthAnnotation;
import com.kbao.kbcelms.user.service.UserService;
import com.kbao.kbcelms.user.vo.UserInfoVO;
import com.kbao.kbcelms.user.vo.UserRoleAuthVO;
import com.kbao.kbcelms.util.ElmsUserUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30 14:03
 */
@Slf4j
@Component
public class ElmsUserAuthInterceptorAdapter implements HandlerInterceptor {


    @Autowired
    @Lazy
    private UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

//        String tenantId = request.getHeader("tenantId");
//        if (StringUtils.isBlank(tenantId)) {
//            tenantId = request.getParameter("tenantId");
//        }
//
//        String userId = SysLoginUtils.getUserId();

        // 查询当前用户所属角色、角色权限
//        UserRoleAuthVO userRoleAuthVO = userService.getUserRoleAuthByUserId(userId, tenantId);
//        UserInfoVO userInfo = userService.getCurrentUserInfo();
        UserInfoVO userInfo = ElmsUserUtils.getUserInfo();
        if (EmptyUtils.isNotEmpty(userInfo)) {
            if (EmptyUtils.isEmpty(userInfo.getUserRoles())) {
                response.setHeader("Content-Type", "application/json;charset=UTF-8");
                PrintWriter writer = response.getWriter();
                writer.write(JSONObject.toJSONString(Result.failed("用户未分配角色")));
                return false;
            }
            return authorityInterceptor(userInfo, response, handler);
        }
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSONObject.toJSONString(Result.failed("未配置企客用户")));
        return false;
    }

    /**
     * 角色权限位校验
     *
     * @param userRoleAuthVO
     * @param response
     * @param handler
     * <AUTHOR>
     * @Date 2025/7/31 9:37
     */
    public boolean authorityInterceptor(UserInfoVO userInfo, HttpServletResponse response, Object handler) {
        try {
            ElmsAuthAnnotation authority = (ElmsAuthAnnotation) ((HandlerMethod) handler).getMethodAnnotation(ElmsAuthAnnotation.class);
            if (authority != null && authority.auths() != null && authority.auths().length > 0) {
                boolean enable = false;
                String[] auths = authority.auths();
                List<String> authList = new ArrayList<>();
                userInfo.getUserRoles().forEach(role -> {
                    if (EmptyUtils.isNotEmpty(role.getRoleAuths())) {
                        role.getRoleAuths().forEach(auth -> {
                            authList.add(auth.getAuthCode());
                        });
                    }
                });
                if (EmptyUtils.isNotEmpty(authList)) {
                    for (String auth : auths) {
                        if (authList.contains(auth)) {
                            enable = true;
                            break;
                        }
                    }
                }

                if (!enable) {
                    log.error("无权限访问");
                    response.setHeader("Content-Type", "application/json;charset=UTF-8");
                    PrintWriter writer = response.getWriter();
                    writer.write(JSONObject.toJSONString(Result.failed("无权限访问")));
                    return false;
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    }
}
