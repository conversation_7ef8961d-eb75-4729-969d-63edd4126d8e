package com.kbao.kbcelms.controller.riskMatrix;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemQuery;
import com.kbao.kbcelms.riskMatrix.bean.ScoreItemRequest;
import com.kbao.kbcelms.riskMatrix.vo.ScoreItemVO;
import com.kbao.kbcelms.riskMatrix.service.ScoreItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 核心评分项管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/elms/scoreItem")
public class ScoreItemController extends BaseController {
    
    @Autowired
    private ScoreItemService scoreItemService;
    
    /**
     * 分页查询评分项列表
     */
    @PostMapping("/page")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "分页查询评分项列表")
    public Result<PageInfo<ScoreItemVO>> getPage(@RequestBody PageRequest<ScoreItemQuery> request) {
        try {
            PageInfo<ScoreItemVO> result = scoreItemService.getPage(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("分页查询评分项列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询评分项详情
     */
    @GetMapping("/{id}")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "查询评分项详情")
    public Result<ScoreItemVO> getById(@PathVariable Long id) {
        try {
            ScoreItemVO result = scoreItemService.getById(id);
            if (result == null) {
                return Result.failed("评分项不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("查询评分项详情失败，ID：{}", id, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存评分项（新增或更新）
     */
    @PostMapping("/save")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "保存", desc = "保存评分项")
    public Result<ScoreItemVO> save(@RequestBody @Validated ScoreItemRequest request) {
        try {
            // 检查编码是否重复
            if (scoreItemService.existsByCode(request.getCode(), request.getId())) {
                return Result.failed("评分项编码已存在，请修改后重试");
            }
            
            String currentUser = getCurrentUser();
            ScoreItemVO result = scoreItemService.save(request, currentUser);
            
            String action = request.getId() != null ? "更新" : "新增";
            return Result.succeed(result, action + "成功");
        } catch (Exception e) {
            log.error("保存评分项失败", e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID删除评分项
     */
    @DeleteMapping("/{id}")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "删除", desc = "删除评分项")
    public Result<Void> deleteById(@PathVariable Long id) {
        try {
            boolean success = scoreItemService.deleteById(id);
            if (success) {
                return Result.succeed(null, "删除成功");
            } else {
                return Result.failed("删除失败，评分项不存在");
            }
        } catch (Exception e) {
            log.error("删除评分项失败，ID：{}", id, e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量删除评分项
     */
    @DeleteMapping("/batch")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "批量删除", desc = "批量删除评分项")
    public Result<Void> deleteByIds(@RequestBody List<Long> ids) {
        try {
            int count = scoreItemService.deleteByIds(ids);
            return Result.succeed(null, "成功删除" + count + "条记录");
        } catch (Exception e) {
            log.error("批量删除评分项失败，IDs：{}", ids, e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据编码查询评分项
     */
    @GetMapping("/code/{code}")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "根据编码查询评分项")
    public Result<ScoreItemVO> getByCode(@PathVariable String code) {
        try {
            ScoreItemVO result = scoreItemService.getByCode(code);
            if (result == null) {
                return Result.failed("评分项不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("根据编码查询评分项失败，编码：{}", code, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据类别查询评分项列表
     */
    @GetMapping("/category/{category}")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "根据类别查询评分项列表")
    public Result<List<ScoreItemVO>> getByCategory(@PathVariable String category) {
        try {
            List<ScoreItemVO> result = scoreItemService.getByCategory(category);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("根据类别查询评分项列表失败，类别：{}", category, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID列表查询评分项
     */
    @PostMapping("/byIds")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "查询", desc = "根据ID列表查询评分项")
    public Result<List<ScoreItemVO>> getByIds(@RequestBody List<Long> ids) {
        try {
            List<ScoreItemVO> result = scoreItemService.getByIds(ids);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("根据ID列表查询评分项失败，IDs：{}", ids, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查编码是否存在
     */
    @GetMapping("/checkCode")
    @LogAnnotation(module = "核心评分项管理", recordRequestParam = true, action = "检查", desc = "检查编码是否存在")
    public Result<Boolean> checkCode(@RequestParam String code, @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = scoreItemService.existsByCode(code, excludeId);
            return Result.succeed(exists, "检查完成");
        } catch (Exception e) {
            log.error("检查编码是否存在失败，编码：{}", code, e);
            return Result.failed("检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        // 这里应该从安全上下文或会话中获取当前用户
        // 暂时返回默认值
        return "admin";
    }
}
