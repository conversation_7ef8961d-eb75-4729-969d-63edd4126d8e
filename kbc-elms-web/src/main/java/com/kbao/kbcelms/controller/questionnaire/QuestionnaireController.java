package com.kbao.kbcelms.controller.questionnaire;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireAnswerBean;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireQueryBean;
import com.kbao.kbcelms.questionnaire.bean.QuestionnaireSaveBean;
import com.kbao.kbcelms.questionnaire.service.QuestionnaireAnswerService;
import com.kbao.kbcelms.questionnaire.service.QuestionnaireService;
import com.kbao.kbcelms.questionnaire.vo.QuestionnaireVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 问卷管理控制器
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/questionnaire")
@Api(tags = "问卷管理")
public class QuestionnaireController extends BaseController {

    @Autowired
    private QuestionnaireService questionnaireService;

    @Autowired
    private QuestionnaireAnswerService questionnaireAnswerService;

    @PostMapping("/page")
    @ApiOperation("分页查询问卷列表")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "查询", desc = "分页查询问卷列表")
    public Result<PageInfo<QuestionnaireVO>> getQuestionnairePage(
            @ApiParam("分页查询参数") @RequestBody @Validated PageRequest<QuestionnaireQueryBean> request
    ) {
        PageInfo<QuestionnaireVO> result = questionnaireService.getQuestionnairePage(request);
        return Result.succeed(result, "查询成功");
    }

    @GetMapping("/{id}")
    @ApiOperation("获取问卷详情")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "查询", desc = "获取问卷详情")
    public Result<QuestionnaireVO> getQuestionnaireDetail(
            @ApiParam("问卷ID") @PathVariable @NotNull Long id
    ) {
        QuestionnaireVO questionnaire = questionnaireService.getQuestionnaireDetail(id);
        return Result.succeed(questionnaire, "查询成功");
    }

    @PostMapping
    @ApiOperation("创建问卷")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "新增", desc = "创建问卷")
    public Result<Long> createQuestionnaire(
            @ApiParam("问卷信息") @RequestBody @Validated QuestionnaireSaveBean saveBean
    ) {
        Long id = questionnaireService.saveQuestionnaire(saveBean);
        return Result.succeed(id, "创建成功");
    }

    @PutMapping("/{id}")
    @ApiOperation("更新问卷")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "修改", desc = "更新问卷")
    public Result<Long> updateQuestionnaire(
            @ApiParam("问卷ID") @PathVariable @NotNull Long id,
            @ApiParam("问卷信息") @RequestBody @Validated QuestionnaireSaveBean saveBean
    ) {
        saveBean.setId(id);
        Long resultId = questionnaireService.saveQuestionnaire(saveBean);
        return Result.succeed(resultId, "更新成功");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除问卷")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "删除", desc = "删除问卷")
    public Result<Boolean> deleteQuestionnaire(
            @ApiParam("问卷ID") @PathVariable @NotNull Long id
    ) {
        Boolean success = questionnaireService.deleteQuestionnaire(id);
        return Result.succeed(success, "删除成功");
    }

    @PostMapping("/answer/submit")
    @ApiOperation("提交问卷答案")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "提交", desc = "提交问卷答案")
    public Result<Boolean> submitQuestionnaireAnswer(
            @ApiParam("问卷答案") @RequestBody @Validated QuestionnaireAnswerBean answerBean
    ) {
        Boolean success = questionnaireAnswerService.submitQuestionnaireAnswer(answerBean);
        return Result.succeed(success, "提交成功");
    }

    @GetMapping("/{questionnaireId}/answer/{enterpriseId}")
    @ApiOperation("查询企业的问卷答案")
    @LogAnnotation(module = "问卷管理", recordRequestParam = true, action = "查询", desc = "查询企业的问卷答案")
    public Result<List<Map<String, Object>>> getEnterpriseAnswers(
            @ApiParam("问卷ID") @PathVariable @NotNull Long questionnaireId,
            @ApiParam("企业ID") @PathVariable @NotNull Long enterpriseId
    ) {
        List<Map<String, Object>> answers = questionnaireAnswerService.getEnterpriseAnswers(questionnaireId, enterpriseId);
        return Result.succeed(answers, "查询成功");
    }
}
