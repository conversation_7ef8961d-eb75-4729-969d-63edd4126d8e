package com.kbao.kbcelms.controller.riskMatrix;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixQuery;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixRequest;
import com.kbao.kbcelms.riskMatrix.service.RiskMatrixLevelService;
import com.kbao.kbcelms.riskMatrix.vo.RiskMatrixVO;
import com.kbao.kbcelms.riskMatrix.service.RiskMatrixService;
import com.kbao.kbcelms.riskMatrix.service.RiskMatrixCategoryService;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 风险矩阵管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/elms/riskMatrix")
public class RiskMatrixController extends BaseController {
    
    @Autowired
    private RiskMatrixService riskMatrixService;

    @Autowired
    private RiskMatrixCategoryService riskMatrixCategoryService;
    
    /**
     * 分页查询风险矩阵列表
     */
    @PostMapping("/page")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "分页查询风险矩阵列表")
    public Result<PageInfo<RiskMatrixVO>> getPage(@RequestBody PageRequest<RiskMatrixQuery> request) {
        try {
            PageInfo<RiskMatrixVO> result = riskMatrixService.getPage(request);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("分页查询风险矩阵列表失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询风险矩阵详情
     */
    @GetMapping("/{id}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "查询风险矩阵详情")
    public Result<RiskMatrixVO> getById(@PathVariable Long id) {
        try {
            RiskMatrixVO result = riskMatrixService.getById(id);
            if (result == null) {
                return Result.failed("风险矩阵不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("查询风险矩阵详情失败，ID：{}", id, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存风险矩阵（新增或更新）
     */
    @PostMapping("/save")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "保存", desc = "保存风险矩阵")
    public Result<Void> save(@RequestBody @Validated RiskMatrixRequest request) {
        try {
            // 检查编码是否重复
            if (riskMatrixService.existsByCode(request.getName(), request.getId())) {
                return Result.failed("矩阵名称已存在，请修改后重试");
            }
            
            String currentUser = getCurrentUser();
            riskMatrixService.save(request, currentUser);
            
            String action = request.getId() != null ? "更新" : "新增";
            return Result.succeed(action + "成功");
        } catch (Exception e) {
            log.error("保存风险矩阵失败", e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID删除风险矩阵
     */
    @DeleteMapping("/{id}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "删除", desc = "删除风险矩阵")
    public Result<Void> deleteById(@PathVariable Long id) {
        try {
            boolean success = riskMatrixService.deleteById(id);
            if (success) {
                return Result.succeed(null, "删除成功");
            } else {
                return Result.failed("删除失败，风险矩阵不存在");
            }
        } catch (Exception e) {
            log.error("删除风险矩阵失败，ID：{}", id, e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量删除风险矩阵
     */
    @DeleteMapping("/batch")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "批量删除", desc = "批量删除风险矩阵")
    public Result<Void> deleteByIds(@RequestBody List<Long> ids) {
        try {
            int count = riskMatrixService.deleteByIds(ids);
            return Result.succeed(null, "成功删除" + count + "条记录");
        } catch (Exception e) {
            log.error("批量删除风险矩阵失败，IDs：{}", ids, e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据编码查询风险矩阵
     */
    @GetMapping("/code/{code}")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "查询", desc = "根据编码查询风险矩阵")
    public Result<RiskMatrixVO> getByCode(@PathVariable String code) {
        try {
            RiskMatrixVO result = riskMatrixService.getByCode(code);
            if (result == null) {
                return Result.failed("风险矩阵不存在");
            }
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            log.error("根据编码查询风险矩阵失败，编码：{}", code, e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查编码是否存在
     */
    @GetMapping("/checkCode")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "检查", desc = "检查编码是否存在")
    public Result<Boolean> checkCode(@RequestParam String code, @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = riskMatrixService.existsByCode(code, excludeId);
            return Result.succeed(exists, "检查完成");
        } catch (Exception e) {
            log.error("检查编码是否存在失败，编码：{}", code, e);
            return Result.failed("检查失败：" + e.getMessage());
        }
    }

    /**
     * 保存风险矩阵类别配置
     */
    @PostMapping("/{matrixId}/categories")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "保存", desc = "保存风险矩阵类别配置")
    public Result<Void> saveCategories(@PathVariable Long matrixId,
                                     @RequestBody @Validated List<RiskMatrixRequest.CategoryRequest> categories) {
        try {
            String currentUser = getCurrentUser();
            boolean success = riskMatrixCategoryService.saveCategoriesForMatrix(matrixId, categories, currentUser);

            if (success) {
                return Result.succeed(null, "类别配置保存成功");
            } else {
                return Result.failed("类别配置保存失败");
            }
        } catch (Exception e) {
            log.error("保存风险矩阵类别配置失败，矩阵ID：{}", matrixId, e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }

    /**
     * 保存类别的档次配置
     */
    @PostMapping("/{matrixId}/categories/{categoryId}/levels")
    @LogAnnotation(module = "风险矩阵管理", recordRequestParam = true, action = "保存", desc = "保存类别档次配置")
    public Result<Void> saveLevelsForCategory(@PathVariable Long matrixId,
                                            @PathVariable Long categoryId,
                                            @RequestBody @Validated List<RiskMatrixRequest.LevelRequest> levels) {
        try {
            String currentUser = getCurrentUser();
            boolean success = riskMatrixCategoryService.saveLevelsForCategory(categoryId, levels, currentUser);

            if (success) {
                return Result.succeed(null, "档次配置保存成功");
            } else {
                return Result.failed("档次配置保存失败");
            }
        } catch (Exception e) {
            log.error("保存类别档次配置失败，矩阵ID：{}，类别ID：{}", matrixId, categoryId, e);
            return Result.failed("保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        return SysLoginUtils.getUserId();
    }
}
