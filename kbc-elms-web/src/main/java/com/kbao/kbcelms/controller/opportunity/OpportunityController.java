package com.kbao.kbcelms.controller.opportunity;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.opportunity.service.OpportunityService;
import com.kbao.kbcelms.opportunity.dto.OpportunityDetailQuery;
import com.kbao.kbcelms.opportunity.vo.OpportunityDetailVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityJoinResponseVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityTaskVO;
import com.kbao.kbcelms.opportunity.vo.OpportunitySuspendVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseRequestVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityCloseReasonVO;
import com.kbao.kbcelms.opportunityprocesslog.vo.OpportunityRestartRequestVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityCompleteTaskVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityDetailRequestVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityDetailResponseVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityProcessProgressVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityTodoVO;
import com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService;
import com.kbao.kbcbpm.process.vo.ActivityInfoVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityTimelineVO;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/opportunity")
@Api(tags = "机会相关接口")
public class OpportunityController {

    @Autowired
    private OpportunityService opportunityService;

    @Autowired
    private OpportunityProcessService opportunityProcessService;

    @ApiOperation(value = "分页查询我的待处理机会任务", notes = "分页查询我的待处理机会任务")
    @PostMapping("/toDoPage")
    public Result<PageInfo<OpportunityTodoVO>> getTodoTasksByUserAndCandidate(@RequestBody PageRequest reqVo) {
        return opportunityService.getTodoTasksByUserAndCandidate(reqVo);
    }

    @ApiOperation(value = "分页查询我的待领取机会任务", notes = "分页查询我的待领取机会任务")
    @PostMapping("/toGetPage")
    public Result<PageInfo<OpportunityTodoVO>> getToGetTasksByUserAndCandidate(@RequestBody PageRequest reqVo) {
        return opportunityService.getToGetTasksByUserAndCandidate(reqVo);
    }

    @ApiOperation(value = "分页查询我的待参与机会任务", notes = "分页查询我的待参与机会任务，查询在项目团队中属于专家角色且join_type为待确认的数据")
    @PostMapping("/toJoinPage")
    public Result<PageInfo<OpportunityTodoVO>> getToJoinPage(@RequestBody PageRequest reqVo) {
        return opportunityService.getToJoinPage(reqVo);
    }

    @ApiOperation(value = "分页查询我参与的机会", notes = "分页查询我参与的机会，判断依据是在项目团队中确定参与，机会流程日志中存在过的人")
    @PostMapping("/toHasJoinPage")
    public Result<PageInfo<OpportunityTodoVO>> getToHasJoin(@RequestBody PageRequest reqVo) {
        return opportunityService.getToHasJoin(reqVo);
    }


    @ApiOperation(value = "分页查询所有机会（总公司统筹）", notes = "分页查询所有机会（总公司统筹）")
    @PostMapping("/toALLPage")
    public Result<PageInfo<OpportunityTodoVO>> getAlloppoPage(@RequestBody PageRequest reqVo) {
        return opportunityService.getAlloppoPage(reqVo);
    }
    

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "领取任务", desc = "分公司领取任务")
    @ApiOperation(value = "领取任务", notes = "分公司领取任务")
    @PostMapping("/acceptTask")
    public Result<String> acceptTask(@RequestBody OpportunityTaskVO taskVO) {
        opportunityService.acceptTask(taskVO.getOpportunityId());
        return Result.succeed("任务领取成功");
    }

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "设置分公司统筹", desc = "设置分公司统筹，任务转交其他统筹")
    @ApiOperation(value = "设置分公司统筹", notes = "设置分公司统筹，任务转交其他统筹")
    @PostMapping("/setBranchCoordination")
    public Result<String> setBranchCoordination(@RequestBody OpportunityTaskVO taskVO) {
        opportunityService.setBranchCoordination(
            taskVO.getOpportunityId(), 
            taskVO.getAssignee(),
            taskVO.getAssigneeRoleType(), 
            taskVO.getAssigneeOrg()
        );
        return Result.succeed("分公司统筹设置成功");
    }

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "设置项目经理", desc = "设置项目经理")
    @ApiOperation(value = "设置项目经理", notes = "设置项目经理")
    @PostMapping("/setOpportunityProjecter")
    public Result<String> setOpportunityProjecter(@RequestBody OpportunityTaskVO taskVO) {
        opportunityService.setOpportunityProjecter(
            taskVO.getOpportunityId(), 
            taskVO.getBusinessTenantId(),
            taskVO.getAssignee(), 
            taskVO.getAssigneeRoleType(), 
            taskVO.getAssigneeOrg()
        );
        return Result.succeed("项目经理设置成功");
    }

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "完成任务", desc = "完成当前任务")
    @ApiOperation(value = "完成任务", notes = "完成当前任务")
    @PostMapping("/completeTask")
    public Result<String> completeTask(@RequestBody OpportunityCompleteTaskVO completeTaskVO) {
        opportunityProcessService.completeTask(
            completeTaskVO.getOpportunityId(),
                SysLoginUtils.getUserId(),
            SysLoginUtils.getUser().getTenantId()
        );
        return Result.succeed("任务完成成功");
    }

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "暂停机会", desc = "暂停机会")
    @ApiOperation(value = "暂停机会", notes = "暂停机会")
    @PostMapping("/suspend")
    public Result<String> suspendOpportunity(@RequestBody OpportunitySuspendVO suspendVO) {
        opportunityService.suspendOpportunity(suspendVO.getOpportunityId(), suspendVO.getReason());
        return Result.succeed("机会暂停成功");
    }

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "恢复机会", desc = "恢复暂停的机会")
    @ApiOperation(value = "恢复机会", notes = "恢复暂停的机会")
    @PostMapping("/resume")
    public Result<String> resumeOpportunity(@RequestBody OpportunitySuspendVO suspendVO) {
        opportunityService.resumeOpportunity(suspendVO.getOpportunityId(), suspendVO.getReason());
        return Result.succeed("机会恢复成功");
    }

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "关闭机会", desc = "关闭机会")
    @ApiOperation(value = "关闭机会", notes = "关闭机会")
    @PostMapping("/close")
    public Result<String> closeOpportunity(@RequestBody OpportunityCloseRequestVO request) {
        opportunityService.closeOpportunity(request);
        return Result.succeed("机会关闭成功");
    }

    @ApiOperation(value = "获取关闭原因类型列表", notes = "获取关闭原因类型列表")
    @PostMapping("/close/reasons")
    public Result<List<OpportunityCloseReasonVO>> getCloseReasonTypes() {
        List<OpportunityCloseReasonVO> reasonList = opportunityService.getCloseReasonTypes();
        return Result.succeed(reasonList, "获取关闭原因类型列表成功");
    }

    @LogAnnotation(module = "机会管理", recordRequestParam = true, action = "重启机会", desc = "重启关闭的机会")
    @ApiOperation(value = "重启机会", notes = "重启关闭的机会")
    @PostMapping("/restart")
    public Result<String> restartOpportunity(@RequestBody OpportunityRestartRequestVO request) {
        opportunityService.restartOpportunity(request);
        return Result.succeed("机会重启成功");
    }

    /**
     * 分页查询机会详情列表
     * @param pageRequest 分页参数
     * @return 机会详情列表
     */
    @ApiOperation(value = "分页查询机会详情列表", notes = "分页查询机会详情列表")
    @PostMapping("/detail/page")
    public Result<PageInfo<OpportunityDetailVO>> queryOpportunityDetails(@RequestBody PageRequest<OpportunityDetailQuery> pageRequest) {
        PageInfo<OpportunityDetailVO> pageInfo = opportunityService.queryOpportunityDetailsPage(pageRequest);
        return Result.succeed(pageInfo, "查询成功");
    }

    @ApiOperation(value = "获取机会流程进度", notes = "根据机会ID获取流程进度信息")
    @PostMapping("/process/progress")
    public Result<List<ActivityInfoVO>> getProcessProgress(@RequestBody OpportunityProcessProgressVO progressVO) {
        List<ActivityInfoVO> progressList = opportunityService.getProcessProgress(progressVO.getOpportunityId());
        return Result.succeed(progressList, "获取流程进度成功");
    }

    @ApiOperation(value = "获取机会时间线", notes = "根据机会ID获取融合流程进度和操作日志的时间线信息")
    @PostMapping("/timeline")
    public Result<List<OpportunityTimelineVO>> getOpportunityTimeline(@RequestBody OpportunityProcessProgressVO progressVO) {
        List<OpportunityTimelineVO> timelineList = opportunityService.getOpportunityTimeline(progressVO.getOpportunityId());
        return Result.succeed(timelineList, "获取时间线成功");
    }

    @ApiOperation(value = "获取机会进度日志", notes = "根据机会ID获取机会进度日志")
    @PostMapping("/progesslog")
    public Result<List<OpportunityTimelineVO>> getProcessProgessLog(@RequestBody OpportunityProcessProgressVO progressVO) {
        List<OpportunityTimelineVO> timelineList = opportunityService.getProcessProgessLog(progressVO.getOpportunityId());
        return Result.succeed(timelineList, "获取时间线成功");
    }

    @ApiOperation(value = "获取机会完整详情", notes = "根据机会ID获取机会信息、机会详情、企业信息")
    @PostMapping("/detail/full")
    public Result<OpportunityDetailResponseVO> getOpportunityFullDetail(@RequestBody OpportunityDetailRequestVO request) {
        OpportunityDetailResponseVO response = opportunityService.getOpportunityFullDetail(request);
        return Result.succeed(response, "获取机会完整详情成功");
    }

    @ApiOperation(value = "参与机会获取邀请人用户信息", notes = "参与机会获取邀请人用户信息")
    @PostMapping("/getOpportunityInviterInfo")
    public Result<OpportunityJoinResponseVO> getOpportunityInviterInfo(@RequestBody OpportunityDetailRequestVO requestVO) {
        OpportunityJoinResponseVO responseVO = opportunityService.getOpportunityInviterInfo(requestVO.getOpportunityId());
        return Result.succeed(responseVO, "获取获取机会邀请人用户信息成功");
    }
}